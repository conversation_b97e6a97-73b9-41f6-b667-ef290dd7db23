import fs from 'fs';
import path from 'path';
import yaml from 'js-yaml';
import 'dotenv/config';
import { AppConfig, YamlConfig, LoggerConfig } from '../src/types/config.js';
import { getLoggerConfig } from '../config/log.js';

// Minimal logger for config errors
const logError = (message: string) => {
  console.error(message);
};

// Paths to configuration files
const CONFIG_PATH = path.join(process.cwd(), 'config.yaml');

export const getRequiredEnv = (key: string): string => {
  const value = process.env[key];
  if (!value) {
    logError(`❌ Required environment variable ${key} is not set. The application will exit.`);
    process.exit(1);
  }
  return value;
};

const getConfigValue = (
  envKey: string,
  yamlValue: string | undefined,
  defaultValue: string,
): string => {
  return process.env[envKey] ?? yamlValue ?? defaultValue;
};

// Read and parse the YAML configuration file
let yamlConfig: YamlConfig = {};
try {
  if (fs.existsSync(CONFIG_PATH)) {
    const fileContents = fs.readFileSync(CONFIG_PATH, 'utf-8');
    yamlConfig = yaml.load(fileContents) as YamlConfig;
  }
} catch (error: unknown) {
  if (error instanceof Error) {
    logError(`❌ Failed to read or parse config.yaml: ${error.message}`);
  } else {
    logError(`❌ Failed to read or parse config.yaml:${error}`);
  }
  process.exit(1);
}

const loggerConfig = getLoggerConfig(yamlConfig);

// Build configuration object combining YAML structure with .env sensitive data
const CONFIG: AppConfig & LoggerConfig = {
  ...loggerConfig,
  MainDatabase: {
    host: getConfigValue('DB_HOST', yamlConfig.MainDatabase?.host, 'localhost'),
    port: parseInt(getConfigValue('DB_PORT', undefined, '5432'), 10),
    name: getConfigValue('DB_NAME', yamlConfig.MainDatabase?.name, 'rishiflow'),
    connectionTimeoutMillis: parseInt(
      getConfigValue(
        'DB_CONNECTION_TIMEOUT',
        yamlConfig.MainDatabase?.connectionTimeoutMillis,
        '2000',
      ),
      10,
    ),
    idleTimeoutMillis: parseInt(
      getConfigValue('DB_IDLE_TIMEOUT', yamlConfig.MainDatabase?.idleTimeoutMillis, '30000'),
      10,
    ),
    maxPool: parseInt(getConfigValue('DB_MAX_POOL', yamlConfig.MainDatabase?.maxPool, '20'), 10),
  },
  DatabaseOwner: {
    user: getConfigValue('DB_OWNER_USER', yamlConfig.DatabaseOwner?.user, 'rishiflow'),
    password: getRequiredEnv('DB_OWNER_PASSWORD'),
  },
  PostgresSuperuser: {
    user: getConfigValue('DB_POSTGRES_USER', yamlConfig.PostgresSuperuser?.user, 'postgres'),
    password: getRequiredEnv('DB_POSTGRES_PASSWORD'),
  },
  ApplicationUser: {
    user: getConfigValue('DB_APP_USER', yamlConfig.ApplicationUser?.user, 'app_user'),
    password: getRequiredEnv('DB_APP_PASSWORD'),
  },
  MigrationUser: {
    user: getConfigValue('DB_MIGRATION_USER', yamlConfig.MigrationUser?.user, 'migration_user'),
    password: getRequiredEnv('DB_MIGRATION_PASSWORD'),
  },
  Server: {
    port: parseInt(getRequiredEnv('SERVER_PORT'), 10),
    host: getConfigValue('SERVER_HOST', yamlConfig.Server?.host, '127.0.0.1'),
  },
  Twilio: {
    accountSid: getRequiredEnv('TWILIO_AUTH_SID') as string,
    authToken: getRequiredEnv('TWILIO_AUTH_TOKEN') as string,
    twilioSandboxNumber: getRequiredEnv('TWILIO_SANDBOX_NUMBER') as string,
  },
  OTP: {
    lifeSpanMins: parseInt(getRequiredEnv('OTP_LIFE_MINS'), 10) * 60 * 1000 || 5 * 60 * 1000,
  },
};

export { CONFIG, CONFIG_PATH };
