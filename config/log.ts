import { LoggerConfig, YamlConfig } from '../src/types/config.js';

/**
 * Builds and validates the logger configuration from a YAML config object.
 *
 * @param {YamlConfig} yamlConfig - The configuration object parsed from YAML.
 * @returns {LoggerConfig} - A validated logger configuration object.
 *
 * @throws {Error} If the provided logger level is invalid.
 * @throws {Error} If the `prettyPrint` option is not a boolean.
 * @throws {Error} If the `filePath` is not a string.
 * @throws {Error} If the rotation interval is invalid.
 * @throws {Error} If the rotation maxFiles is not a valid number.
 *
 * @example
 * const yamlConfig = {
 *   Logger: {
 *     level: "debug",
 *     prettyPrint: false,
 *     filePath: "./logs/server.log",
 *     rotation: { interval: "7d", maxFiles: "10" }
 *   }
 * };
 *
 * const config = getLoggerConfig(yamlConfig);
 * console.log(config.Logger.level); // "debug"
 */
export const getLoggerConfig = (yamlConfig: YamlConfig): LoggerConfig => {
  const loggerConfig: LoggerConfig = {
    Logger: {
      level: yamlConfig.Logger?.level || 'info',
      prettyPrint: yamlConfig.Logger?.prettyPrint ?? true,
      filePath: yamlConfig.Logger?.filePath || './logs/app.log',
      enableConsole: yamlConfig.Logger?.enableConsole ?? true,
      enableFile: yamlConfig.Logger?.enableFile ?? true,
      rotation: {
        interval: yamlConfig.Logger?.rotation?.interval || '1d',
        maxFiles: yamlConfig.Logger?.rotation?.maxFiles || '7',
      },
      prettyOptions: {
        colorize: yamlConfig.Logger?.prettyOptions?.colorize ?? true,
        translateTime: yamlConfig.Logger?.prettyOptions?.translateTime || 'SYS:standard',
        ignore: yamlConfig.Logger?.prettyOptions?.ignore || 'pid,hostname',
        singleLine: yamlConfig.Logger?.prettyOptions?.singleLine ?? false,
        hideObject: yamlConfig.Logger?.prettyOptions?.hideObject ?? false,
      },
    },
  };

  // Validation
  const validLevels = ['trace', 'debug', 'info', 'warn', 'error', 'fatal'];
  if (!validLevels.includes(loggerConfig.Logger.level)) {
    throw new Error(`Invalid logger level: ${loggerConfig.Logger.level}`);
  }

  if (typeof loggerConfig.Logger.prettyPrint !== 'boolean') {
    throw new Error(`Invalid prettyPrint value: ${loggerConfig.Logger.prettyPrint}`);
  }

  if (typeof loggerConfig.Logger.filePath !== 'string') {
    throw new Error(`Invalid filePath value: ${loggerConfig.Logger.filePath}`);
  }

  const validIntervals = ['1d', '7d', '30d'];
  if (!validIntervals.includes(loggerConfig.Logger.rotation.interval)) {
    throw new Error(`Invalid rotation interval: ${loggerConfig.Logger.rotation.interval}`);
  }

  if (isNaN(parseInt(loggerConfig.Logger.rotation.maxFiles, 10))) {
    throw new Error(`Invalid rotation maxFiles: ${loggerConfig.Logger.rotation.maxFiles}`);
  }

  return loggerConfig;
};
