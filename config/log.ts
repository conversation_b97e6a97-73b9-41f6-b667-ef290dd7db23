import { LoggerConfig, YamlConfig } from '../src/types/config.js';

// Function to validate and get logger configuration
export const getLoggerConfig = (yamlConfig: YamlConfig): LoggerConfig => {
  const loggerConfig: LoggerConfig = {
    Logger: {
      level: yamlConfig.Logger?.level || 'info',
      prettyPrint: yamlConfig.Logger?.prettyPrint ?? true,
      filePath: yamlConfig.Logger?.filePath || './logs/app.log',
      rotation: {
        interval: yamlConfig.Logger?.rotation?.interval || '1d',
        maxFiles: yamlConfig.Logger?.rotation?.maxFiles || '7',
      },
    },
  };

  // Validation
  const validLevels = ['trace', 'debug', 'info', 'warn', 'error', 'fatal'];
  if (!validLevels.includes(loggerConfig.Logger.level)) {
    throw new Error(`Invalid logger level: ${loggerConfig.Logger.level}`);
  }

  if (typeof loggerConfig.Logger.prettyPrint !== 'boolean') {
    throw new Error(`Invalid prettyPrint value: ${loggerConfig.Logger.prettyPrint}`);
  }

  if (typeof loggerConfig.Logger.filePath !== 'string') {
    throw new Error(`Invalid filePath value: ${loggerConfig.Logger.filePath}`);
  }

  const validIntervals = ['1d', '7d', '30d'];
  if (!validIntervals.includes(loggerConfig.Logger.rotation.interval)) {
    throw new Error(`Invalid rotation interval: ${loggerConfig.Logger.rotation.interval}`);
  }

  if (isNaN(parseInt(loggerConfig.Logger.rotation.maxFiles, 10))) {
    throw new Error(`Invalid rotation maxFiles: ${loggerConfig.Logger.rotation.maxFiles}`);
  }

  return loggerConfig;
};
