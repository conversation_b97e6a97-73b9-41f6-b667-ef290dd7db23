import isValidPhoneNumber from '../../../src/utils/phone-number-validator';

describe('Phone Number Validator', () => {
  it('should return true for a valid Indian phone number', () => {
    // Using a number with a known valid mobile prefix (e.g., 98)
    expect(isValidPhoneNumber('+************')).toBe(true);
  });

  it('should return true for a valid Kenyan phone number with spaces', () => {
    // The validator function should trim whitespace
    expect(isValidPhoneNumber(' +************ ')).toBe(true);
  });

  it('should return true for a valid Australian phone number with spaces', () => {
    // The validator function should trim whitespace
    expect(isValidPhoneNumber(' +************** ')).toBe(true);
  });

  it('should return true for a valid Tanzanian phone number with spaces', () => {
    expect(isValidPhoneNumber('+*********** 678')).toBe(true);
  });

  it('should return true for a valid US phone number', () => {
    expect(isValidPhoneNumber('+***********')).toBe(true);
  });

  it('should return false for a phone number without a country code', () => {
    // The library requires an international format with '+' to be reliable
    expect(isValidPhoneNumber('9876543210')).toBe(false);
  });

  it('should return false for a number with an invalid prefix for its country', () => {
    expect(isValidPhoneNumber('+011234567890')).toBe(false);
  });

  it('should return false for a phone number with too few digits', () => {
    expect(isValidPhoneNumber('+9198765432')).toBe(false);
  });

  it('should return false for a phone number with too many digits', () => {
    expect(isValidPhoneNumber('+************1')).toBe(false);
  });

  it('should return false for a phone number with non-digit characters', () => {
    expect(isValidPhoneNumber('+9198765a4321')).toBe(false);
  });

  it('should return false for an empty string', () => {
    expect(isValidPhoneNumber('')).toBe(false);
  });

  it('should return false for a string with only whitespace', () => {
    expect(isValidPhoneNumber('  ')).toBe(false);
  });
});
