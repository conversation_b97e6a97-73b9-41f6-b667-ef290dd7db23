import { hashString, compareHash } from '../../../src/utils/bcrypt-hash-compare';

describe('Bcrypt Hash and Compare', () => {
  const testString = 'testPassword';

  describe('hashString', () => {
    it('should return a string', async () => {
      const hashedString = await hashString(testString);
      expect(typeof hashedString).toBe('string');
    });

    it('should not return the original string', async () => {
      const hashedString = await hashString(testString);
      expect(hashedString).not.toBe(testString);
    });
  });

  describe('compareHash', () => {
    it('should return true for a correct string and hash', async () => {
      const hashedString = await hashString(testString);
      const result = await compareHash(testString, hashedString);
      expect(result).toBe(true);
    });

    it('should return false for an incorrect string and hash', async () => {
      const hashedString = await hashString(testString);
      const result = await compareHash('wrongPassword', hashedString);
      expect(result).toBe(false);
    });
  });
});
