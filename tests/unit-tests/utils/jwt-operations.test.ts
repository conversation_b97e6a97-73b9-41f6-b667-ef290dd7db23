import { generateJWTToken, verifyJWT, issueTokens } from '../../../src/utils/jwt-operations';
import * as queries from '../../../src/database/queries';

// Mock the database queries
jest.mock('../../../src/database/queries', () => ({
  storeJWTToken: jest.fn(),
}));

describe('JWT Operations', () => {
  const phoneNumber = '+1234567890';
  const secret = 'test-secret';
  const expiresIn = '1h';

  describe('generateJWTToken', () => {
    it('should generate a token with phone_number', () => {
      const token = generateJWTToken({ phone_number: phoneNumber }, secret, expiresIn);
      expect(typeof token).toBe('string');
    });

    it('should generate a token with phone_number and jti', () => {
      const jti = 'test-jti';
      const token = generateJWTToken({ phone_number: phoneNumber, jti }, secret, expiresIn);
      expect(typeof token).toBe('string');
    });
  });

  describe('verifyJWT', () => {
    it('should verify a valid token', () => {
      const token = generateJWTToken({ phone_number: phoneNumber }, secret, expiresIn);
      const decoded = verifyJWT(token, secret);
      expect(decoded).toHaveProperty('phone_number', phoneNumber);
    });

    it('should throw an error for an invalid token', () => {
      const token = generateJWTToken({ phone_number: phoneNumber }, secret, expiresIn);
      expect(() => verifyJWT(token, 'wrong-secret')).toThrow();
    });
  });

  describe('issueTokens', () => {
    beforeAll(() => {
      process.env.JWT_ACCESS_TOKEN_SECRET = 'access-secret';
      process.env.JWT_REFRESH_TOKEN_SECRET = 'refresh-secret';
      process.env.JWT_ACCESS_EXPIRES_IN = '15m';
      process.env.JWT_REFRESH_EXPIRES_IN = '7d';
    });

    afterAll(() => {
      delete process.env.JWT_ACCESS_TOKEN_SECRET;
      delete process.env.JWT_REFRESH_TOKEN_SECRET;
      delete process.env.JWT_ACCESS_EXPIRES_IN;
      delete process.env.JWT_REFRESH_EXPIRES_IN;
    });

    it('should return an access_token and a refresh_token', async () => {
      const { access_token, refresh_token } = await issueTokens(phoneNumber);
      expect(typeof access_token).toBe('string');
      expect(typeof refresh_token).toBe('string');
    });

    it('should call storeJWTToken', async () => {
      await issueTokens(phoneNumber);
      expect(queries.storeJWTToken).toHaveBeenCalled();
    });
  });
});
