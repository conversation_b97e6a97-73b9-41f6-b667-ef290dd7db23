import { Request, Response, NextFunction } from 'express';
import verifyOTP from '../../../src/controllers/verify-otp-controller';
import * as queries from '../../../src/database/queries';
import { BadRequestError } from '../../../src/utils/errors';

jest.mock('../../../src/database/queries');

describe('Verify OTP Controller', () => {
  let req: Partial<Request>;
  let res: Partial<Response>;
  let next: NextFunction;
  let json: jest.Mock;
  let status: jest.Mock;

  beforeEach(() => {
    req = {
      body: {
        phone_number: '+911234567890',
        otp: '123456',
      },
    };
    json = jest.fn();
    status = jest.fn(() => ({ json }));
    res = {
      status,
    };
    next = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should call next with BadRequestError if phone_number is missing', async () => {
    delete req.body.phone_number;
    await verifyOTP(req as Request, res as Response, next);
    expect(next).toHaveBeenCalledWith(new BadRequestError('Phone number and OTP are required.'));
  });

  it('should call next with BadRequestError if otp is missing', async () => {
    delete req.body.otp;
    await verifyOTP(req as Request, res as Response, next);
    expect(next).toHaveBeenCalledWith(new BadRequestError('Phone number and OTP are required.'));
  });

  it('should call next with BadRequestError if stored OTP does not exist', async () => {
    (queries.retrieveValidOTP as jest.Mock).mockResolvedValue(null);
    await verifyOTP(req as Request, res as Response, next);
    expect(next).toHaveBeenCalledWith(new BadRequestError('Invalid or expired OTP.'));
  });

  it('should call next with BadRequestError if OTP does not match', async () => {
    (queries.retrieveValidOTP as jest.Mock).mockResolvedValue({
      otp: { otp_code: '654321' },
    });
    await verifyOTP(req as Request, res as Response, next);
    expect(next).toHaveBeenCalledWith(new BadRequestError('Invalid or expired OTP.'));
  });

  it('should return 200 on successful OTP verification', async () => {
    (queries.retrieveValidOTP as jest.Mock).mockResolvedValue({
      otp: { otp_code: '123456' },
    });
    await verifyOTP(req as Request, res as Response, next);
    expect(status).toHaveBeenCalledWith(200);
  });

  it('should call next with an error on database error', async () => {
    const dbError = new Error('DB error');
    (queries.retrieveValidOTP as jest.Mock).mockRejectedValue(dbError);
    await verifyOTP(req as Request, res as Response, next);
    expect(next).toHaveBeenCalledWith(dbError);
  });
});
