import { Request, Response, NextFunction } from 'express';
import refreshToken from '../../../src/controllers/refresh-token-controller';
import * as jwt from '../../../src/utils/jwt-operations';
import * as queries from '../../../src/database/queries';
import * as bcrypt from '../../../src/utils/bcrypt-hash-compare';
import { JsonWebTokenError, TokenExpiredError } from 'jsonwebtoken';

describe('Refresh Token Controller', () => {
  let req: Partial<Request>;
  let res: Partial<Response>;
  let next: NextFunction;

  beforeEach(() => {
    req = {
      body: {},
    };
    res = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    };
    next = jest.fn();
  });

  it('should return 401 if no refresh token is provided', async () => {
    await refreshToken(req as Request, res as Response, next);
    expect(res.status).toHaveBeenCalledWith(401);
    expect(res.json).toHaveBeenCalledWith({ error: 'No refresh token' });
  });

  it('should return 401 if refresh token is invalid', async () => {
    req.body = { refresh_token: 'invalidtoken' };
    jest.spyOn(jwt, 'verifyJWT').mockImplementation(() => {
      throw new JsonWebTokenError('Unauthorized. Please login again.');
    });
    await refreshToken(req as Request, res as Response, next);
    expect(res.status).toHaveBeenCalledWith(401);
    expect(res.json).toHaveBeenCalledWith({ error: 'Unauthorized. Please login again.' });
  });

  it('should return 401 if refresh token is expired', async () => {
    req.body = { refresh_token: 'expiredtoken' };
    jest.spyOn(jwt, 'verifyJWT').mockImplementation(() => {
      throw new TokenExpiredError('Unauthorized. Please login again.', new Date());
    });
    await refreshToken(req as Request, res as Response, next);
    expect(res.status).toHaveBeenCalledWith(401);
    expect(res.json).toHaveBeenCalledWith({ error: 'Unauthorized. Please login again.' });
  });

  it('should return 401 if jti does not match', async () => {
    req.body = { refresh_token: 'validtoken' };
    jest.spyOn(jwt, 'verifyJWT').mockReturnValue({ phone_number: '123', jti: '123' });
    jest
      .spyOn(queries, 'getUserToken')
      .mockResolvedValue({ refresh_token: 'hashedtoken', jti: '456' });
    await refreshToken(req as Request, res as Response, next);
    expect(res.status).toHaveBeenCalledWith(401);
    expect(res.json).toHaveBeenCalledWith({ error: 'Unauthorized. Please login again.' });
  });

  it('should return 200 and new tokens if refresh token is valid', async () => {
    req.body = { refresh_token: 'validtoken' };
    jest.spyOn(jwt, 'verifyJWT').mockReturnValue({ phone_number: '123', jti: '123' });
    jest
      .spyOn(queries, 'getUserToken')
      .mockResolvedValue({ refresh_token: 'hashedtoken', jti: '123' });
    jest.spyOn(bcrypt, 'compareHash').mockResolvedValue(true);
    jest
      .spyOn(jwt, 'issueTokens')
      .mockResolvedValue({ access_token: 'newaccess', refresh_token: 'newrefresh' });
    await refreshToken(req as Request, res as Response, next);
    expect(res.status).toHaveBeenCalledWith(200);
    expect(res.json).toHaveBeenCalledWith({
      access_token: 'newaccess',
      refresh_token: 'newrefresh',
    });
  });
});
