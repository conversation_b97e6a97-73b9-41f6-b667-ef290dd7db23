import { NextFunction, Response } from 'express';
import authMiddleware, { CustomRequest } from '../../../src/controllers/auth-middleware';
import * as jwt from '../../../src/utils/jwt-operations';
import { AppError } from '../../../src/utils/errors.js';

describe('Auth Middleware', () => {
  let req: Partial<CustomRequest>;
  let res: Partial<Response>;
  let next: NextFunction;

  beforeEach(() => {
    req = {
      headers: {},
    };
    res = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    };
    next = jest.fn();
  });

  it('should call next with an AppError if authorization header is missing', () => {
    authMiddleware(req as CustomRequest, res as Response, next);
    expect(next).toHaveBeenCalledWith(
      new AppError('Unauthorized: Authorization header missing', 401),
    );
  });

  it('should call next with an AppError if token format is invalid', () => {
    req.headers = { authorization: 'Invalid token' };
    authMiddleware(req as CustomRequest, res as Response, next);
    expect(next).toHaveBeenCalledWith(new AppError('Unauthorized: Invalid token format', 401));
  });

  it('should call next with an error if token is invalid', () => {
    req.headers = { authorization: 'Bearer invalidtoken' };
    const error = new Error('Invalid token');
    jest.spyOn(jwt, 'verifyJWT').mockImplementation(() => {
      throw error;
    });
    authMiddleware(req as CustomRequest, res as Response, next);
    expect(next).toHaveBeenCalledWith(error);
  });

  it('should call next if token is valid', () => {
    req.headers = { authorization: 'Bearer validtoken' };
    const decoded = { userId: '123' };
    jest.spyOn(jwt, 'verifyJWT').mockReturnValue(decoded);
    authMiddleware(req as CustomRequest, res as Response, next);
    expect(req.user).toBe(decoded);
    expect(next).toHaveBeenCalled();
  });
});
