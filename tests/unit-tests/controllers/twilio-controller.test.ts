import twilio from 'twilio';
import sendTwilioOTPSms from '../../../src/controllers/twilio-controller';
import { CONFIG } from '../../../config';

// Mock the twilio library
jest.mock('twilio', () => {
  const messages = {
    create: jest.fn(),
  };
  return jest.fn(() => ({
    messages,
  }));
});

describe('Twilio Controller', () => {
  const recipient = '+1234567890';
  const otp = '123456';

  it('should call client.messages.create with the correct parameters', async () => {
    const mockTwilio = twilio as jest.Mock;
    const mockCreate = mockTwilio().messages.create;

    await sendTwilioOTPSms(recipient, otp);

    expect(mockCreate).toHaveBeenCalledWith({
      body: `Your verification code is ${otp}`,
      from: `whatsapp:${CONFIG.Twilio.twilioSandboxNumber}`,
      to: `whatsapp:${recipient}`,
    });
  });

  it('should throw an error if client.messages.create throws an error', async () => {
    const mockTwilio = twilio as jest.Mock;
    const mockCreate = mockTwilio().messages.create;
    const errorMessage = 'Twilio error';
    mockCreate.mockRejectedValue(new Error(errorMessage));

    await expect(sendTwilioOTPSms(recipient, otp)).rejects.toThrow(errorMessage);
  });
});
