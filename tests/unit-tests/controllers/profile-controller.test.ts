import { Request, Response } from 'express';
import { getUserProfile, updateUserProfile } from '../../../src/controllers/profile-controller';

describe('Profile Controller', () => {
  let req: Partial<Request>;
  let res: Partial<Response>;

  beforeEach(() => {
    req = {};
    res = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    };
  });

  describe('getUserProfile', () => {
    it('should return 200 with a success message', () => {
      getUserProfile(req as Request, res as Response);
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith({ message: 'You are authorized to this endpoint' });
    });
  });

  describe('updateUserProfile', () => {
    it('should return 200 with a success message', () => {
      updateUserProfile(req as Request, res as Response);
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith({ message: 'You are authorized to this endpoint' });
    });
  });
});
