import { getRequiredEnv } from '../../../config/index.js';

// Mock console.error to spy on it during tests
const mockConsoleError = jest.spyOn(console, 'error').mockImplementation(() => {});

describe('Configuration: getRequiredEnv', () => {
  const originalEnv = { ...process.env };

  // Spy on process.exit and replace implementation with a no-op
  const mockExit = jest
    .spyOn(process, 'exit')
    // cleaner typing: cast jest.fn() to the right function signature
    .mockImplementation((() => {
      return undefined as never;
    }) as unknown as typeof process.exit);

  beforeEach(() => {
    process.env = { ...originalEnv };
    jest.clearAllMocks();
  });

  afterAll(() => {
    process.env = originalEnv;
    mockExit.mockRestore();
    mockConsoleError.mockRestore();
  });

  test('should return the environment variable value when it exists', () => {
    const key = 'MY_TEST_VAR';
    const value = 'hello world';
    process.env[key] = value;

    const result = getRequiredEnv(key);

    expect(result).toBe(value);
    expect(mockConsoleError).not.toHaveBeenCalled();
    expect(mockExit).not.toHaveBeenCalled();
  });

  test('should call console.error and process.exit when environment variable is missing', () => {
    const key = 'MISSING_VAR';
    delete process.env[key];

    getRequiredEnv(key);

    expect(mockConsoleError).toHaveBeenCalledWith(
      `❌ Required environment variable ${key} is not set. The application will exit.`,
    );
    expect(mockExit).toHaveBeenCalledWith(1);
  });
});
