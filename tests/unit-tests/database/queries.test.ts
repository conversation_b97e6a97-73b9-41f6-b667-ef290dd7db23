import { createUser, insertOTP, retrieveValidOTP, userExists } from '../../../src/database/queries';
import { UserData } from '../../../src/types/user';
import { OtpData } from '../../../src/types/otp';

const mockQuery = jest.fn();

jest.mock('../../../src/database/db-connection', () => ({
  getPool: () => ({
    query: mockQuery,
  }),
}));

describe('Database Queries', () => {
  beforeEach(() => {
    mockQuery.mockClear();
  });

  describe('createUser', () => {
    it('should execute the correct SQL query to create a user', async () => {
      const userData: UserData = {
        phone_number: '+1234567890',
        first_name: 'Avi',
        last_name: '<PERSON><PERSON><PERSON><PERSON>',
        otp: {
          created_at: '',
          expires_at: '',
          otp_code: '',
        },
      };
      mockQuery.mockResolvedValue({ rows: [], rowCount: 1 });

      await createUser(userData);

      expect(mockQuery).toHaveBeenCalledWith('INSERT INTO users (user_data) VALUES ($1)', [
        userData,
      ]);
    });
  });

  describe('insertOTP', () => {
    it('should execute the correct SQL query to insert an OTP', async () => {
      const phoneNumber = '+1234567890';
      const otpData: OtpData = {
        otp_code: '123456',
        created_at: new Date().toISOString(),
        expires_at: new Date(Date.now() + 5 * 60 * 1000).toISOString(),
      };
      const expectedRow = { user_data: { ...otpData } };
      mockQuery.mockResolvedValue({ rows: [expectedRow], rowCount: 1 });

      const result = await insertOTP(phoneNumber, otpData);

      expect(mockQuery).toHaveBeenCalledWith(expect.stringContaining('UPDATE users'), [
        phoneNumber,
        JSON.stringify(otpData),
      ]);
      expect(result).toEqual(expectedRow);
    });
  });

  describe('retrieveValidOTP', () => {
    it('should return OTP data if a valid OTP is found', async () => {
      const phoneNumber = '+1234567890';
      const otpData = { otp: { otp_code: '123456' }, is_expired: false };
      mockQuery.mockResolvedValue({ rows: [otpData], rowCount: 1 });

      const result = await retrieveValidOTP(phoneNumber);

      expect(mockQuery).toHaveBeenCalledWith(expect.stringContaining('SELECT'), [phoneNumber]);
      expect(result).toEqual(otpData);
    });

    it('should return null if no OTP is found', async () => {
      const phoneNumber = '+1234567890';
      mockQuery.mockResolvedValue({ rows: [], rowCount: 0 });

      const result = await retrieveValidOTP(phoneNumber);

      expect(result).toBeNull();
    });
  });

  describe('userExists', () => {
    it('should return true if the user exists', async () => {
      const phoneNumber = '+1234567890';
      mockQuery.mockResolvedValue({ rows: [{ exists: true }], rowCount: 1 });

      const result = await userExists(phoneNumber);

      expect(mockQuery).toHaveBeenCalledWith(expect.stringContaining('SELECT EXISTS'), [
        phoneNumber,
      ]);
      expect(result).toBe(true);
    });

    it('should return false if the user does not exist', async () => {
      const phoneNumber = '+1234567890';
      mockQuery.mockResolvedValue({ rows: [{ exists: false }], rowCount: 1 });

      const result = await userExists(phoneNumber);

      expect(result).toBe(false);
    });
  });
});
