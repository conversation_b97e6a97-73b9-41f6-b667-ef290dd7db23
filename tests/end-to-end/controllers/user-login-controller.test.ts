import request from 'supertest';
import express from 'express';
import userLogin from '../../../src/controllers/user-login-controller.js';

// Mock dependencies
jest.mock('../../../src/database/queries.js', () => ({
  userExists: jest.fn(),
  retrieveValidOTP: jest.fn(),
  insertOTP: jest.fn(),
}));

jest.mock('../../../src/utils/otp-generator.js', () => jest.fn(() => '1234'));
jest.mock('../../../src/utils/phone-number-validator.js', () => jest.fn());
jest.mock('../../../src/controllers/twilio-controller.js', () => jest.fn());
jest.mock('../../../src/utils/logger.js', () => ({ info: jest.fn() }));

import { userExists, retrieveValidOTP, insertOTP } from '../../../src/database/queries.js';
import isValidPhoneNumber from '../../../src/utils/phone-number-validator.js';
import sendTwilioOTPSms from '../../../src/controllers/twilio-controller.js';

import { AppError, InternalServerError } from '../../../src/utils/errors.js';

const app = express();
app.use(express.json());
app.post('/login', userLogin);
app.use((err: Error, _req: express.Request, res: express.Response, _next: express.NextFunction) => {
  if (err instanceof AppError) {
    res.status(err.statusCode).json({ message: err.message });
  } else {
    const internalError = new InternalServerError();
    res.status(internalError.statusCode).json({ message: internalError.message });
  }
});

describe('POST /login', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should return 400 for invalid phone number', async () => {
    (isValidPhoneNumber as jest.Mock).mockReturnValue(false);

    const res = await request(app).post('/login').send({ phone_number: 'invalid' });

    expect(res.status).toBe(400);
    expect(res.body).toEqual({ message: 'Invalid phone number format.' });
  });

  it('should return 404 if user does not exist', async () => {
    (isValidPhoneNumber as jest.Mock).mockReturnValue(true);
    (userExists as jest.Mock).mockResolvedValue(false);

    const res = await request(app).post('/login').send({ phone_number: '+254740707743' });

    expect(res.status).toBe(404);
    expect(res.body).toEqual({ message: 'The user does not exist' });
  });

  it('should return 200 if valid OTP already exists', async () => {
    (isValidPhoneNumber as jest.Mock).mockReturnValue(true);
    (userExists as jest.Mock).mockResolvedValue(true);
    (retrieveValidOTP as jest.Mock).mockResolvedValue({
      is_expired: false,
      otp_data: { otp_code: '123456' },
    });

    const res = await request(app).post('/login').send({ phone_number: '+254740707743' });

    expect(res.status).toBe(200);
    expect(res.body).toEqual({
      message: 'OTP already sent and is still valid. Please use the existing OTP.',
    });
  });

  it('should generate and send OTP if no valid OTP exists', async () => {
    (isValidPhoneNumber as jest.Mock).mockReturnValue(true);
    (userExists as jest.Mock).mockResolvedValue(true);
    (retrieveValidOTP as jest.Mock).mockResolvedValue(null);
    (insertOTP as jest.Mock).mockResolvedValue(undefined);
    (sendTwilioOTPSms as jest.Mock).mockResolvedValue(undefined);

    const res = await request(app).post('/login').send({ phone_number: '+254740707743' });

    expect(res.status).toBe(200);
    expect(res.body).toEqual({ message: 'OTP has been sent sucessfully' });
    expect(insertOTP).toHaveBeenCalled();
    expect(sendTwilioOTPSms).toHaveBeenCalledWith('+254740707743', '1234');
  });

  it('should return 500 on internal server error', async () => {
    (isValidPhoneNumber as jest.Mock).mockReturnValue(true);
    (userExists as jest.Mock).mockImplementation(() => {
      throw new Error('DB crash');
    });

    const res = await request(app).post('/login').send({ phone_number: '+254740707743' });

    expect(res.status).toBe(500);
    expect(res.body).toEqual({ message: 'Internal Server Error' });
  });
});
