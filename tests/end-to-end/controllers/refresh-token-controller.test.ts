import request from 'supertest';
import { createApp } from '../../../src/app';
import { issueTokens } from '../../../src/utils/jwt-operations';
import { getUserToken } from '../../../src/database/queries';
import { compareHash } from '../../../src/utils/bcrypt-hash-compare';

jest.mock('../../../src/utils/jwt-operations', () => ({
  ...jest.requireActual('../../../src/utils/jwt-operations'),
  issueTokens: jest.fn(),
}));
jest.mock('../../../src/database/queries');
jest.mock('../../../src/utils/bcrypt-hash-compare');

describe('POST /refresh', () => {
  const app = createApp();

  it('should return 401 if no refresh token is provided', async () => {
    const res = await request(app).post('/refresh').send({});
    expect(res.status).toBe(401);
    expect(res.body).toEqual({ error: 'No refresh token' });
  });

  it('should return 401 if the refresh token is invalid', async () => {
    const res = await request(app).post('/refresh').send({ refresh_token: 'invalidtoken' });
    expect(res.status).toBe(401);
  });

  it('should return 200 and new tokens if the refresh token is valid', async () => {
    const phoneNumber = '+123456789';
    const jti = 'some-jti';

    process.env.JWT_REFRESH_TOKEN_SECRET = 'test-secret';

    const { generateJWTToken } = jest.requireActual('../../../src/utils/jwt-operations');
    const token = generateJWTToken(
      { phone_number: phoneNumber, jti },
      process.env.JWT_REFRESH_TOKEN_SECRET,
      '1h',
    );

    (getUserToken as jest.Mock).mockResolvedValue({ jti, refresh_token: 'hashedtoken' });
    (compareHash as jest.Mock).mockResolvedValue(true);
    (issueTokens as jest.Mock).mockResolvedValue({
      access_token: 'newaccess',
      refresh_token: 'newrefresh',
    });

    const res = await request(app).post('/refresh').send({ refresh_token: token });

    expect(res.status).toBe(200);
    expect(res.body).toEqual({ access_token: 'newaccess', refresh_token: 'newrefresh' });
  });
});
