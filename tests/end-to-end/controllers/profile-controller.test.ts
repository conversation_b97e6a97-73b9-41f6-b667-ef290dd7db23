import request from 'supertest';
import { createApp } from '../../../src/app';
import { generateJWTToken } from '../../../src/utils/jwt-operations';

describe('/profile endpoints', () => {
  const app = createApp();
  process.env.JWT_ACCESS_TOKEN_SECRET = 'test-secret';

  describe('GET /profile', () => {
    it('should return 401 if no token is provided', async () => {
      const res = await request(app).get('/profile');
      expect(res.status).toBe(401);
      expect(res.body).toEqual({ error: 'Unauthorized: Authorization header missing' });
    });

    it('should return 200 if a valid token is provided', async () => {
      const token = generateJWTToken(
        { phone_number: '+123456789' },
        process.env.JWT_ACCESS_TOKEN_SECRET as string,
        '1h',
      );
      const res = await request(app).get('/profile').set('Authorization', `Bearer ${token}`);
      expect(res.status).toBe(200);
      expect(res.body).toEqual({ message: 'You are authorized to this endpoint' });
    });
  });

  describe('POST /profile', () => {
    it('should return 401 if no token is provided', async () => {
      const res = await request(app).post('/profile');
      expect(res.status).toBe(401);
      expect(res.body).toEqual({ error: 'Unauthorized: Authorization header missing' });
    });

    it('should return 200 if a valid token is provided', async () => {
      const token = generateJWTToken(
        { phone_number: '+123456789' },
        process.env.JWT_ACCESS_TOKEN_SECRET as string,
        '1h',
      );
      const res = await request(app).post('/profile').set('Authorization', `Bearer ${token}`);
      expect(res.status).toBe(200);
      expect(res.body).toEqual({ message: 'You are authorized to this endpoint' });
    });
  });
});
