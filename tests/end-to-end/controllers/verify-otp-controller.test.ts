import request from 'supertest';
import { createApp } from '../../../src/app';
import { retrieveValidOTP } from '../../../src/database/queries';
import { issueTokens } from '../../../src/utils/jwt-operations';

jest.mock('../../../src/database/queries');
jest.mock('../../../src/utils/jwt-operations');

describe('POST /verify-otp', () => {
  const app = createApp();

  it('should return 400 if phone_number or otp are not provided', async () => {
    const res = await request(app).post('/verify-otp').send({});
    expect(res.status).toBe(400);
    expect(res.body).toEqual({ error: 'Phone number and OTP are required.' });
  });

  it('should return 400 if the OTP is invalid or expired', async () => {
    (retrieveValidOTP as jest.Mock).mockResolvedValue(null);
    const res = await request(app)
      .post('/verify-otp')
      .send({ phone_number: '+123456789', otp: '123456' });
    expect(res.status).toBe(400);
    expect(res.body).toEqual({ error: 'Invalid or expired OTP.' });
  });

  it('should return 200 and tokens if the OTP is valid', async () => {
    const phoneNumber = '+123456789';
    const otp = '123456';
    (retrieveValidOTP as jest.Mock).mockResolvedValue({ otp: { otp_code: otp } });
    (issueTokens as jest.Mock).mockResolvedValue({
      access_token: 'newaccess',
      refresh_token: 'newrefresh',
    });

    const res = await request(app).post('/verify-otp').send({ phone_number: phoneNumber, otp });

    expect(res.status).toBe(200);
    expect(res.body).toEqual({ access_token: 'newaccess', refresh_token: 'newrefresh' });
  });
});
