import { Request, Response, NextFunction } from 'express';
import { retrieveValidOTP } from '../database/queries.js';
import { BadRequestError } from '../utils/errors.js';

/**
 * @openapi
 * /verify-otp:
 *   post:
 *     summary: Verify OTP to complete login
 *     description: |
 *      Verifies the One-Time Password (OTP) previously sent to the user's phone number.
 *      If the OTP is valid and not expired, the login process is completed and an authentication token is issued.
 *     tags:
 *      - Authentication
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - phone_number
 *               - otp
 *             properties:
 *               phone_number:
 *                 type: string
 *                 description: The user's phone number.
 *               otp:
 *                 type: string
 *                 description: The OTP received by the user.
 *             example:
 *               phone_number: "+911234567890"
 *               otp: "123456"
 *     responses:
 *       200:
 *         description: OTP successfully verified and user authenticated.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Authentication successful.
 *       400:
 *         description: Bad request — missing phone number, OTP, malformed JSON, or invalid/expired OTP.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *             examples:
 *               missingFields:
 *                 value:
 *                   error: "Phone number and OTP are required."
 *               invalidOtp:
 *                 value:
 *                   error: "Invalid or expired OTP."
 *               malformedJson:
 *                 value:
 *                   error: "Malformed JSON in request body"
 *       500:
 *         description: Internal server error during OTP verification.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Internal server error.
 */

/**
 * Verifies the OTP for a given phone number.
 * @param req - The Express request object, containing the phone number and OTP.
 * @param res - The Express response object.
 * @param next - The Express next middleware function.
 */
const verifyOTP = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { phone_number, otp } = req.body;

    if (!phone_number || !otp) {
      throw new BadRequestError('Phone number and OTP are required.');
    }

    const cleanPhoneStr = phone_number.replace(/\s+/g, '');
    const storedOtpData = await retrieveValidOTP(cleanPhoneStr);

    if (!storedOtpData || otp !== storedOtpData.otp.otp_code) {
      throw new BadRequestError('Invalid or expired OTP.');
    }

    // JWT Implementation here
    return res.status(200).json({ message: 'Authentication successful.' });
  } catch (err) {
    next(err);
  }
};

export default verifyOTP;
