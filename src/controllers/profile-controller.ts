import { Request, Response } from 'express';

/**
 * @openapi
 * /profile:
 *   get:
 *     summary: Retrieve user profile data
 *     description: |
 *      Retrieves user profile information. A successful retrieval requires valid authorization headers/token.
 *     tags:
 *      - User
 *     responses:
 *       200:
 *         description: OK response.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: object
 *                   example: Authentication successful.
 *       401:
 *         description: Unauthorized.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Unauthorized.
 *       500:
 *         description: Internal server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Internal server error.
 */

const getUserProfile = (_req: Request, res: Response) => {
  return res.status(200).json({ message: 'You are authorized to this endpoint' });
};

/**
 * @openapi
 * /profile:
 *   post:
 *     summary: Update user Profile
 *     description: |
 *      Updates the user's profile information.  A successful update requires valid credentials.
 *     tags:
 *      - User
 *     responses:
 *       200:
 *         description: OK response.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Profile updated sucessfully.
 *       401:
 *         description: Unauthorized.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Oops, something went wrong.
 */
const updateUserProfile = (_req: Request, res: Response) => {
  return res.status(200).json({ message: 'You are authorized to this endpoint' });
};
export { getUserProfile, updateUserProfile };
