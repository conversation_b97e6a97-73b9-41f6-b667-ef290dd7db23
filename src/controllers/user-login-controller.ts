import { NextFunction, Request, Response } from 'express';
import { insertOTP, retrieveValidOTP, userExists } from '../database/queries.js';
import generateOTP from '../utils/otp-generator.js';
import { OtpData } from '../types/otp.js';
import sendTwilioOTPSms from './twilio-controller.js';
import isValidPhoneNumber from '../utils/phone-number-validator.js';
import { CONFIG } from '../../config/index.js';
// Swagger Documentation

/**
 * @openapi
 * /login:
 *   post:
 *     summary: Request OTP for login
 *     description: |
 *      Initiates the login process by generating and sending a One-Time Password (OTP)
 *      to the user's phone number.
 *      The OTP must be verified via the /verify-otp endpoint to complete login.
 *     tags:
 *      - Authentication
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - phone_number
 *             properties:
 *               phone_number:
 *                 type: string
 *                 description: The user's phone number (e.g +91XXXXXXXXXX format).
 *             example:
 *               phone_number: "+911234567890"
 *     responses:
 *       200:
 *         description: OTP was successfully generated.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               oneOf:
 *                 - properties:
 *                     message:
 *                       type: string
 *                       example: "Your OTP has been generated and sent to your whatsapp number"
 *                 - properties:
 *                     message:
 *                       type: string
 *                       example: "An active OTP has already been sent. Please wait before requesting a new one."
 *       400:
 *         description: Invalid phone number.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Invalid phone number format."
 *       404:
 *         description: User record does not exist.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "User does not exist."
 *       500:
 *         description: Internal Server Error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Oops, something went wrong."
 */

// Check if a user exists in our database and return error if a user does not exist
const userLogin = async (req: Request, res: Response, next: NextFunction) => {
  const { phone_number } = req.body;

  /** Check if phone number is valid */
  const isPhoneNumberValid = isValidPhoneNumber(phone_number);
  if (!isPhoneNumberValid) {
    return res.status(400).json({ message: 'Invalid phone number format.' });
  }
  try {
    /** Check if the user details exists in the database; */
    const exists = await userExists(phone_number);
    if (!exists) {
      return res.status(404).json({ message: 'The user does not exist' });
    }

    // Check if a user has a valid OTP in the database to prevent abusing twilio API;
    const otpRecord = await retrieveValidOTP(phone_number);
    if (otpRecord && !otpRecord.is_expired) {
      return res.status(200).json({
        message: 'OTP already sent and is still valid. Please use the existing OTP.',
      });
    }

    // Generate and send OTP
    const otp = generateOTP();
    const now = new Date();
    const expires = new Date(now.getTime() + CONFIG.OTP.lifeSpanMins);
    const otpData: OtpData = {
      // phone_number,
      otp_code: otp,
      created_at: now.toISOString(),
      expires_at: expires.toISOString(),
    };

    // Store the OTP and send the SMS in a single logical unit
    await insertOTP(phone_number, otpData);
    await sendTwilioOTPSms(phone_number, otpData.otp_code);

    return res.status(200).json({ message: 'OTP has been sent sucessfully' });
  } catch (err) {
    next(err);
  }
};
export default userLogin;
