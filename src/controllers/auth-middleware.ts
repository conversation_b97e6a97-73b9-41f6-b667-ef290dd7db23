/**
 * @file This middleware function authenticates incoming requests by verifying JWT tokens.
 * It extracts the JWT token from the 'Authorization' header, validates it, and attaches
 * the decoded user information to the request object for downstream handlers.
 * If the token is missing, malformed, or invalid, it throws an Unauthorized error.
 */

import { Request, NextFunction, Response } from 'express';
import { verifyJWT } from '../utils/jwt-operations.js';
import { JwtPayload } from 'jsonwebtoken';
import { AppError } from '../utils/errors.js';

// Extend Express Request type
export interface CustomRequest extends Request {
  user?: string | JwtPayload;
}

/**
 * Middleware to verify JWT access tokens from the Authorization header.
 * Attaches decoded user info to the request object if verification succeeds.
 * Sends an error response if authentication fails.
 *
 * @param {CustomRequest} req - The incoming request object, extended to include user info.
 * @param {Response} _res - The response object (unused here).
 * @param {NextFunction} next - The callback to pass control to the next middleware.
 */
const authMiddleware = (req: CustomRequest, _res: Response, next: NextFunction) => {
  try {
    // Retrieve the Authorization header
    const authHeader = req.headers['authorization'];
    if (!authHeader) {
      throw new AppError('Unauthorized: Authorization header missing', 401);
    }

    const tokenParts = authHeader.split(' ');
    if (tokenParts.length !== 2 || tokenParts[0] !== 'Bearer') {
      throw new AppError('Unauthorized: Invalid token format', 401);
    }
    const extractedToken = tokenParts[1];

    const decoded = verifyJWT(extractedToken, process.env.JWT_ACCESS_TOKEN_SECRET as string);
    req.user = decoded;
    next();
  } catch (err) {
    next(err);
  }
};
export default authMiddleware;
