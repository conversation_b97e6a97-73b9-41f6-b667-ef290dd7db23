import { NextFunction, Request, Response } from 'express';
import { issueTokens, verifyJWT } from '../utils/jwt-operations.js';
import { getUserToken } from '../database/queries.js';
import pkg from 'jsonwebtoken';
import { compareHash } from '../utils/bcrypt-hash-compare.js';
const { TokenExpiredError, JsonWebTokenError } = pkg;

/**
 * @openapi
 * /refresh:
 *   post:
 *     summary: Refresh access token endpoint
 *     description: |
 *       Regenerates a new access token using a valid refresh token.
 *     tags:
 *       - Authentication
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - refresh_token
 *             properties:
 *               refresh_token:
 *                 type: string
 *                 description: The refresh token received after successful login.
 *             example:
 *               refresh_token: 8gf022g8f74gf87784gf873qgf874gfo8743gf87o4
 *     responses:
 *       200:
 *         description: Successfully issued new tokens.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 access_token:
 *                   type: string
 *                   example: 9698948geo2x7r9t27rxrvvg
 *                 refresh_token:
 *                   type: string
 *                   example: 8gf022g8f74gf87784gf873qgf874gfo8743gf87o4
 *       401:
 *         description: Invalid or expired refresh token.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Unauthorized. Please login to continue.
 *       500:
 *         description: Internal server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Oops, something went wrong.
 */

const refreshToken = async (req: Request, res: Response, next: NextFunction) => {
  const { refresh_token } = req.body;
  if (!refresh_token) return res.status(401).json({ error: 'No refresh token' });

  try {
    const payload = verifyJWT(
      refresh_token,
      process.env.JWT_REFRESH_TOKEN_SECRET as string,
    ) as JWTValues;
    const retrievedUserToken = await getUserToken(payload.phone_number);
    if (retrievedUserToken && retrievedUserToken.jti === payload.jti) {
      const isValidToken = await compareHash(refresh_token, retrievedUserToken.refresh_token);
      if (isValidToken) {
        // regenerate access_token & refresh_token
        const newToken = await issueTokens(payload.phone_number);
        return res.status(200).json(newToken);
      }
    }

    return res.status(401).json({ error: 'Unauthorized. Please login again.' });
  } catch (err) {
    if (err instanceof TokenExpiredError || err instanceof JsonWebTokenError) {
      return res.status(401).json({ error: 'Unauthorized. Please login again.' });
    }
    next(err);
  }
};

export default refreshToken;
