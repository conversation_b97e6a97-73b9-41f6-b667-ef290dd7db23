import twilio from 'twilio';
import { CONFIG } from '../../config/index.js';
// --- Configuration ---
const accountSid: string = CONFIG.Twilio.accountSid;
const authToken: string = CONFIG.Twilio.authToken;

// Your Twilio Sandbox WhatsApp number (the 'from' number)
const twilioSandboxNumber: string = CONFIG.Twilio.twilioSandboxNumber;
const client = twilio(accountSid, authToken);
const sendTwilioOTPSms = async (recipient: string, otp: string) => {
  try {
    await client.messages.create({
      body: `Your verification code is ${otp}`,
      from: `whatsapp:${twilioSandboxNumber}`,
      to: `whatsapp:${recipient}`,
    });
  } catch (err) {
    if (err instanceof Error) {
      throw err; // preserve stack trace
    }
    throw new Error(String(err));
  }
};
export default sendTwilioOTPSms;
