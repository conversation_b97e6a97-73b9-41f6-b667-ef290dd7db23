import { createApp } from './app.js';
import { CONFIG } from '../config/index.js';
import LOGGER from './utils/logger.js';
import db from './database/db-connection.js';

/**
 * Bootstraps and starts the HTTP server.
 * Ensures database connectivity before serving requests.
 * Handles graceful shutdown and critical error scenarios.
 */
const startServer = async () => {
  try {
    // Verify database connection before starting the server
    await db.testConnection();

    // Create and configure the Express application
    const app = createApp();
    const { port, host } = CONFIG.Server;

    // Start listening for incoming HTTP requests
    const server = app.listen(port, host, () => {
      LOGGER.info(`🚀 Server running at http://${host}:${port}`);
      LOGGER.info(`📖 Swagger Docs available at http://${host}:${port}/docs`);
    });

    /**
     * Gracefully shuts down the server and database connection pool.
     * Triggered by termination signals (SIGINT, SIGTERM).
     */
    const gracefulShutdown = async (signal: string) => {
      LOGGER.info(`Received ${signal}. Shutting down gracefully...`);

      // Stop accepting new requests, finish ongoing requests
      server.close(async () => {
        LOGGER.info('✅ HTTP server closed.');

        // Close database pool to avoid open connections
        await db.close();

        // Log shutdown completion
        LOGGER.info('✅ Shutdown complete. Exiting process.');
        process.exit(0); // Normal exit
      });
    };

    // Handle OS termination signals
    process.on('SIGINT', () => gracefulShutdown('SIGINT')); // e.g., Ctrl+C
    process.on('SIGTERM', () => gracefulShutdown('SIGTERM')); // e.g., kill command or cloud provider shutdown

    // Handle unexpected server-level errors
    server.on('error', error => {
      LOGGER.error(`❌ Server error: ${error}`);
      process.exit(1); // Abnormal exit
    });
  } catch (error) {
    // Fatal error during startup (e.g., DB not reachable)
    LOGGER.error(
      `❌ Failed to start server. Please check the logs above for details. Error: ${error}`,
    );
    process.exit(1); // Abnormal exit
  }
};

//Entry point: start the server
startServer();
