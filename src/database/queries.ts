import { OtpData } from '../types/otp.js';
import { UserData } from '../types/user.js';
import db from './db-connection.js';

/**
 * Inserts a new user into the users table.
 * @param data The user data to insert.
 * @returns A promise that resolves with the query result.
 */
export const createUser = async (data: UserData) => {
  const query = `INSERT INTO users (user_data) VALUES ($1)`;
  // Note: The data object must be passed inside an array for the driver.
  return db.getPool().query(query, [data]);
};

/**
 * Inserts a new OTP record into the otp table.
 * @param data The OTP data to insert.
 * @returns A promise that resolves with the query result.
 */
export const insertOTP = async (phone_number: string, data: OtpData) => {
  const query = `
     UPDATE users
     SET user_data = jsonb_set(
       user_data,
       '{otp}',
       to_jsonb($2::json),
       true
     )
     WHERE user_data->>'phone_number' = $1
     RETURNING user_data;
   `;

  const values = [
    phone_number, // $1 → phone_number to match
    JSON.stringify(data), // $2 → the otp object (code, createdAt, expiresAt)
  ];

  const result = await db.getPool().query(query, values);
  return result.rows[0] || null;
};

/**
 * Retrieves the latest valid OTP for a given phone number.
 * @param phoneNumber The phone number to look up.
 * @returns A promise that resolves with the OTP data or null if not found.
 */
export const retrieveValidOTP = async (
  phoneNumber: string,
): Promise<{ otp: OtpData; is_expired: boolean } | null> => {
  const query = `
     SELECT
       user_data->'otp' AS otp,
       COALESCE(
         ((user_data->'otp'->>'expires_at')::timestamptz < NOW()),
         false
       ) AS is_expired
     FROM users
     WHERE
       user_data->>'phone_number' = $1
       AND user_data ? 'otp'
     ORDER BY
       (user_data->'otp'->>'created_at')::timestamptz DESC
     LIMIT 1
   `;
  const values = [phoneNumber];
  const result = await db.getPool().query(query, values);

  if (result.rows.length === 0) {
    return null; // no user or no otp found
  }

  return result.rows[0];
};

/**
 * Deletes an OTP record by its ID.
 * @param id The ID of the OTP record to delete.
 * @returns A promise that resolves when the operation is complete.
 */
export const deleteOTPById = async (id: string): Promise<void> => {
  const query = `DELETE FROM otp WHERE id = $1`;
  await db.getPool().query(query, [id]);
};

/**
 * Checks if a user exists in the database
 * @param phone_number The phone_number is a unique identifier of a user.
 * @returns A boolean. True if a user exists and false if a user doens't.
 */
export const userExists = async (phoneNumber: string): Promise<boolean> => {
  const query = `
    SELECT EXISTS (
      SELECT 1
      FROM usersss
      WHERE user_data->>'phone_number' = $1
    ) AS exists;
  `;

  const values = [phoneNumber];
  const result = await db.getPool().query(query, values);

  return result.rows[0]?.exists || false;
};
