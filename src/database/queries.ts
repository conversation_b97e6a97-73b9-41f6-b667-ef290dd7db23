import { OtpData } from '../types/otp.js';
import { UserData } from '../types/user.js';
import db from './db-connection.js';

/**
 * Inserts a new user into the users table.
 * @param data The user data to insert.
 * @returns A promise that resolves with the query result.
 */
export const createUser = async (data: UserData) => {
  const query = `INSERT INTO users (user_data) VALUES ($1)`;
  // Note: The data object must be passed inside an array for the driver.
  return db.getPool().query(query, [data]);
};

/**
 * Inserts a new OTP record into the otp table.
 * @param data The OTP data to insert.
 * @returns A promise that resolves with the query result.
 */
export const insertOTP = async (phone_number: string, data: OtpData) => {
  const query = `
     UPDATE users
     SET user_data = jsonb_set(
       user_data,
       '{otp}',
       to_jsonb($2::json),
       true
     )
     WHERE user_data->>'phone_number' = $1
     RETURNING user_data;
   `;

  const values = [
    phone_number, // $1 → phone_number to match
    JSON.stringify(data), // $2 → the otp object (code, createdAt, expiresAt)
  ];

  const result = await db.getPool().query(query, values);
  return result.rows[0] || null;
};

/**
 * Retrieves the latest valid OTP for a given phone number.
 * @param phoneNumber The phone number to look up.
 * @returns A promise that resolves with the OTP data or null if not found.
 */
export const retrieveValidOTP = async (
  phoneNumber: string,
): Promise<{ otp: OtpData; is_expired: boolean } | null> => {
  const query = `
     SELECT
       user_data->'otp' AS otp,
       COALESCE(
         ((user_data->'otp'->>'expires_at')::timestamptz < NOW()),
         false
       ) AS is_expired
     FROM users
     WHERE
       user_data->>'phone_number' = $1
       AND user_data ? 'otp'
     ORDER BY
       (user_data->'otp'->>'created_at')::timestamptz DESC
     LIMIT 1
   `;
  const values = [phoneNumber];
  const result = await db.getPool().query(query, values);

  if (result.rows.length === 0) {
    return null; // no user or no otp found
  }

  return result.rows[0];
};

/**
 * Checks if a user exists in the database
 * @param phone_number The phone_number is a unique identifier of a user.
 * @returns A boolean. True if a user exists and false if a user doens't.
 */
export const userExists = async (phoneNumber: string): Promise<boolean> => {
  const query = `
    SELECT EXISTS (
      SELECT 1
      FROM users
      WHERE user_data->>'phone_number' = $1
    ) AS exists;
  `;

  const values = [phoneNumber];
  const result = await db.getPool().query(query, values);

  return result.rows[0]?.exists || false;
};

// JWT Operations
/**
 * Stores the generated refresh_token and jti
 * @param phone_number -> unique identifier used to identify the user to store the values
 */
export const storeJWTToken = async (phoneNumber: string, refreshToken: string, jti: string) => {
  const query = `
      UPDATE users
      SET user_data = jsonb_set(
        user_data,
        '{token}',
        to_jsonb($2::json),
        true
      )
      WHERE user_data->>'phone_number' = $1
      RETURNING user_data;
    `;
  const values = [phoneNumber, JSON.stringify({ refresh_token: refreshToken, jti: jti })];

  const result = await db.getPool().query(query, values);
  return result.rows[0] || null;
};

// retrieve token [jwt & jti]
/**
 * This function is used to retrieve user refresh_token stored in the db
 * @param phone_number -> unique identifier used to retrive the value
 * @returns refresh_token & jti
 */
export const getUserToken = async (
  phoneNumber: string,
): Promise<{ refresh_token: string; jti: string } | null> => {
  const query = `
    SELECT user_data->'token' AS token
    FROM users
    WHERE user_data->>'phone_number' = $1
    LIMIT 1;
  `;

  const values = [phoneNumber];
  const result = await db.getPool().query(query, values);

  // If no result or token is null, return null
  if (!result.rows[0] || !result.rows[0].token) {
    return null;
  }

  return result.rows[0].token;
};
