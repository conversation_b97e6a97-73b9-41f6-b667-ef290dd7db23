import { Pool, PoolClient } from 'pg';
import { CONFIG } from '../../config/index.js';
import LOGGER from '../utils/logger.js';

/**
 * Database class to manage PostgreSQL connections using a connection pool.
 *
 * Implements the Singleton pattern to ensure only one instance of the
 * connection pool exists across the entire application.
 */
class Database {
  // Holds the single instance of Database (Singleton)
  private static instance: Database;

  // The connection pool for managing PostgreSQL connections
  private pool: Pool;

  /**
   * Private constructor prevents direct instantiation from outside the class.
   * Initializes a new PostgreSQL connection pool with config values.
   */
  private constructor() {
    this.pool = new Pool({
      host: CONFIG.MainDatabase.host,
      port: CONFIG.MainDatabase.port,
      user: CONFIG.ApplicationUser.user,
      password: CONFIG.ApplicationUser.password,
      database: CONFIG.MainDatabase.name,
      // Maximum number of connection pool to the database [Default 20]
      max: CONFIG.MainDatabase.maxPool || 20,
      // Drop the connection to the database after a period of time [Default 30 sec]
      idleTimeoutMillis: CONFIG.MainDatabase.idleTimeoutMillis || 30000,
      // Connection timeout to the database [Default 2 sec]
      connectionTimeoutMillis: CONFIG.MainDatabase.connectionTimeoutMillis || 2000,
    });

    this.pool.on('error', err => {
      LOGGER.error('Unexpected error on idle client', err);
      process.exit(-1);
    });
  }

  /**
   * Provides the singleton instance of the Database class.
   * Creates a new instance if one does not already exist.
   */
  public static getInstance(): Database {
    if (!Database.instance) {
      Database.instance = new Database();
    }
    return Database.instance;
  }

  public async testConnection(): Promise<void> {
    let client: PoolClient | undefined;
    try {
      client = await this.pool.connect();
      const result = await client.query('SELECT NOW()');
      LOGGER.info('✅ Database connection successful at:', result.rows[0].now);
    } catch (err) {
      LOGGER.error(
        '❌ Database connection error. Please check config.yaml and .env files and ensure the database container is running.',
      );
      throw err;
    } finally {
      // Always release the client back to the pool to prevent leaks
      client?.release();
    }
  }

  /**
   * Returns the active connection pool.
   * Use this pool to run queries elsewhere in the application.
   */
  public getPool(): Pool {
    return this.pool;
  }

  /**
   * Gracefully closes the connection pool.
   * Should be called during application shutdown to free resources.
   */
  public async close(): Promise<void> {
    LOGGER.info('🔻 Closing database connection pool...');
    await this.pool.end();
    LOGGER.info('✅ Database connection pool closed.');
  }
}

// Export the singleton instance of Database
const db = Database.getInstance();
export default db;
