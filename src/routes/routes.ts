import express from 'express';
import verifyOTP from '../controllers/verify-otp-controller.js';
import userLogin from '../controllers/user-login-controller.js';
import authMiddleware from '../controllers/auth-middleware.js';
import { getUserProfile, updateUserProfile } from '../controllers/profile-controller.js';
import refreshToken from '../controllers/refresh-token-controller.js';

const router = express.Router(); // Create a new router instance

// For verifying the OTP sent to the user
router.post('/verify-otp', verifyOTP);

// login endpoint
router.post('/login', userLogin);

// refresh token endpoints
router.post('/refresh', refreshToken);

// Protected Endpoints
// profile route
router.post('/profile', authMiddleware, updateUserProfile);
router.get('/profile', authMiddleware, getUserProfile);

export default router; // Export router for use in the main app (app.ts)
