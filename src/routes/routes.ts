import express from 'express';
import verifyOTP from '../controllers/verify-otp-controller.js';
import userLogin from '../controllers/user-login-controller.js';

const router = express.Router(); // Create a new router instance

// For verifying the OTP sent to the user
router.post('/verify-otp', verifyOTP);

// login endpoint
router.post('/login', userLogin);
export default router; // Export router for use in the main app (app.ts)
