//interface which create a blue print for objects
//the data configuration of server will be pasted into the objects (the object implement the interfaces)
export interface AppConfig {
  MainDatabase: {
    host: string;
    port: number;
    name: string;
    maxPool: number;
    idleTimeoutMillis: number;
    connectionTimeoutMillis: number;
  };
  DatabaseOwner: {
    user: string;
    password: string;
  };
  PostgresSuperuser: {
    user: string;
    password: string;
  };
  ApplicationUser: {
    user: string;
    password: string;
  };
  MigrationUser: {
    user: string;
    password: string;
  };
  Server: {
    port: number;
    host: string;
  };
  Twilio: {
    accountSid: string;
    authToken: string;
    twilioSandboxNumber: string;
  };
}

// Interface for YAML configuration structure (non-sensitive data only)
export interface YamlConfig {
  MainDatabase?: {
    host?: string;
    name?: string;
    maxPool?: string;
    idleTimeoutMillis?: string;
    connectionTimeoutMillis?: string;
  };
  DatabaseOwner?: {
    user?: string;
  };
  PostgresSuperuser?: {
    user?: string;
  };
  ApplicationUser?: {
    user?: string;
  };
  MigrationUser?: {
    user?: string;
  };
  Server?: {
    host?: string;
  };
  Logger?: {
    level?: 'trace' | 'debug' | 'info' | 'warn' | 'error' | 'fatal';
    prettyPrint?: boolean;
    filePath?: string;
    rotation?: {
      interval?: '1d' | '7d' | '30d';
      maxFiles?: string;
    };
  };
}

export interface LoggerConfig {
  Logger: {
    level: 'trace' | 'debug' | 'info' | 'warn' | 'error' | 'fatal';
    prettyPrint: boolean;
    filePath: string;
    rotation: {
      interval: '1d' | '7d' | '30d';
      maxFiles: string;
    };
  };
}
