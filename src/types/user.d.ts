/**
 * @file Defines the structure for user data.
 */

import { OtpData } from './otp.js';

/**
 * Represents the structure of a user object.
 */
export interface UserData {
  /** The user's phone number, in international format. */
  phone_number: string;

  /** The user's first name. */
  first_name: string;

  /** The user's last name. */
  last_name: string;

  /** Generated OTP */
  otp: OtpData;
}
