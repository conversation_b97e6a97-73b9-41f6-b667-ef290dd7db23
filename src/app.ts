import express, { Application } from 'express';
import router from './routes/routes.js';
import helmet from 'helmet';
import cors from 'cors';
import LOGGER from './utils/logger.js';
import { AppError, InternalServerError } from './utils/errors.js';
import swaggerDocs from './routes/swagger.js';

export const createApp = (): Application => {
  // Initialize Express application
  const app = express();

  // Global middleware
  app.use(helmet()); // Set security-related HTTP headers
  app.use(cors()); // Enable Cross-Origin Resource Sharing
  app.use(express.json()); // Parse incoming JSON requests into req.body

  //  Catch malformed JSON right after express.json()
  app.use(
    (err: Error, _req: express.Request, res: express.Response, next: express.NextFunction) => {
      if (err instanceof SyntaxError && 'body' in err) {
        LOGGER.error('Malformed JSON:', err.message);
        return res.status(400).json({ error: 'Malformed JSON in request body' });
      }
      next(err); // pass to other error handlers
    },
  );
  app.disable('x-powered-by'); // Hide "X-Powered-By" header to avoid exposing server details

  // Register routes
  app.use('/', router);

  // Swagger Docs api
  swaggerDocs(app);

  // Centralized error handler
  app.use(
    (err: Error, _req: express.Request, res: express.Response, _next: express.NextFunction) => {
      LOGGER.error(err.stack);

      if (err instanceof AppError) {
        res.status(err.statusCode).json({ message: err.message });
      } else {
        const internalError = new InternalServerError();
        res.status(internalError.statusCode).json({ message: internalError.message });
      }
    },
  );

  return app;
};
