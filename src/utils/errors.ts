// Base class for custom application errors with HTTP status codes
class AppError extends Error {
  public readonly statusCode: number;

  constructor(message: string, statusCode: number) {
    super(message); // Initialize built-in Error
    this.statusCode = statusCode; // Set HTTP status code
    Object.setPrototypeOf(this, new.target.prototype); // Fix prototype chain
  }
}

// 500 Internal Server Error
class InternalServerError extends AppError {
  constructor(message = 'Internal Server Error') {
    super(message, 500);
  }
}

// 400 Bad Request Error
class BadRequestError extends AppError {
  constructor(message = 'Bad Request') {
    super(message, 400);
  }
}

//Database error
class DbError extends AppError {
  public readonly code?: string;

  constructor(message = 'Database Error', code?: string, statusCode = 500) {
    super(message, statusCode);
    this.code = code;
  }
}
export { AppError, InternalServerError, BadRequestError, DbError };
