import jwt from 'jsonwebtoken';
import crypto from 'crypto';
import { InternalServerError } from './errors.js';
import { storeJWTToken } from '../database/queries.js';
import ms from 'ms';
import { hashString } from './bcrypt-hash-compare.js';

const generateJWTToken = (
  val: JWTValues,
  secretKey: string,
  expiresIn: ms.StringValue | number,
) => {
  if (!val.jti) {
    return jwt.sign({ phone_number: val.phone_number }, secretKey, {
      expiresIn: expiresIn,
    });
  }
  return jwt.sign(val, secretKey, {
    expiresIn: expiresIn,
  });
};

const verifyJWT = (token: string, secretKey: string) => {
  return jwt.verify(token, secretKey);
};
const issueTokens = async (phoneNumber: string) => {
  const jwtAccessExpiry = process.env.JWT_ACCESS_EXPIRES_IN as ms.StringValue;
  const jwtRefreshExpiry = process.env.JWT_REFRESH_EXPIRES_IN as ms.StringValue;
  const accessToken = generateJWTToken(
    { phone_number: phoneNumber },
    process.env.JWT_ACCESS_TOKEN_SECRET as string,
    jwtAccessExpiry,
  );
  const randomStr = crypto.randomBytes(32).toString('hex');
  const refreshValues: JWTValues = { phone_number: phoneNumber, jti: randomStr };
  const refreshToken = generateJWTToken(
    refreshValues,
    process.env.JWT_REFRESH_TOKEN_SECRET as string,
    jwtRefreshExpiry,
  );

  // store refresh token and jti [ jwt id ] values
  try {
    const hashedRefreshToken = await hashString(refreshToken);
    storeJWTToken(phoneNumber, hashedRefreshToken, refreshValues.jti!);
  } catch {
    throw new InternalServerError('An error occured');
  }
  return { access_token: accessToken, refresh_token: refreshToken };
};
export { generateJWTToken, verifyJWT, issueTokens };
