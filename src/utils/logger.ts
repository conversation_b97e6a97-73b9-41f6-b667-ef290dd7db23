import { pino } from 'pino';
import fs from 'fs/promises';
import { createStream } from 'rotating-file-stream';
import path from 'path';
import { CONFIG } from '../../config/index.js';

// Extract logger settings from the validated CONFIG
const { level, filePath, rotation } = CONFIG.Logger;

// Ensure the logs folder exists before writing (synchronous to avoid race conditions)
fs.mkdir(path.dirname(filePath), { recursive: true });

// Create a rotating file stream for log persistence
export const logDest = createStream(path.basename(filePath), {
  path: path.dirname(filePath), // Directory where logs will be stored
  interval: rotation.interval, // Rotation interval (e.g., "1d", "7d")
  maxFiles: parseInt(rotation.maxFiles, 10), // Max number of rotated log files to keep
});

// Define base logger options
// In test mode: silence logs completely
// Otherwise: use the configured log level
const loggerOptions =  { level };

// Configure multiple logging streams
// 1. Always log to file via `logDest`
// 2. In development (non-prod, non-test), also log to console with pretty-printing
const streams = [{ level: level as pino.Level, stream: logDest }];

if (process.env.NODE_ENV !== 'production' && process.env.NODE_ENV !== 'test') {
  // Add pretty console output for developer readability
  streams.push({
    level: level as pino.Level,
    stream: pino.transport({
      target: 'pino-pretty', // Pretty-print logs
      options: {
        colorize: true, // Enable colored output
        translateTime: 'SYS:standard', // Human-readable timestamp
        ignore: 'pid,hostname', // Omit process ID and hostname from logs
      },
    }),
  });
}

// Create the logger instance with multistream support (file + console)
const LOGGER = pino(loggerOptions, pino.multistream(streams));

export default LOGGER;
