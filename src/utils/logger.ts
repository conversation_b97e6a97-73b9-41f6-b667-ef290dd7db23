import { pino } from 'pino';
import fs from 'fs/promises';
import { createStream } from 'rotating-file-stream';
import path from 'path';
import { CONFIG } from '../../config/index.js';

// Extract logger settings from the validated CONFIG
const { level, filePath, rotation, enableConsole, enableFile, prettyPrint, prettyOptions } =
  CONFIG.Logger;

// Logger options - test environment overrides to silent
const loggerOptions = process.env.NODE_ENV === 'test' ? { level: 'silent' } : { level };

// Multi-stream setup
const streams: Array<{ level: pino.Level; stream: any }> = [];

// Add file stream if enabled
if (enableFile && filePath) {
  // Ensure logs folder exists (synchronous to avoid top-level await)
  fs.mkdir(path.dirname(filePath), { recursive: true });

  // Setup rotating file stream
  const fileStream = createStream(path.basename(filePath), {
    path: path.dirname(filePath),
    interval: rotation.interval as any, // Type assertion for rotating-file-stream compatibility
    maxFiles: parseInt(rotation.maxFiles, 10),
  });

  streams.push({
    level: level as pino.Level,
    stream: fileStream,
  });
}

// Add console stream if enabled
if (enableConsole) {
  if (prettyPrint) {
    // Pretty console output
    streams.push({
      level: level as pino.Level,
      stream: pino.transport({
        target: 'pino-pretty',
        options: {
          colorize: prettyOptions.colorize,
          translateTime: prettyOptions.translateTime,
          ignore: prettyOptions.ignore,
          singleLine: prettyOptions.singleLine,
          hideObject: prettyOptions.hideObject,
        },
      }),
    });
  } else {
    // Raw JSON console output
    streams.push({
      level: level as pino.Level,
      stream: process.stdout,
    });
  }
}

// Create logger with multistream or fallback to basic logger
const LOGGER =
  streams.length > 0 ? pino(loggerOptions, pino.multistream(streams)) : pino(loggerOptions);

export default LOGGER;
