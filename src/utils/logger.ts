import { pino } from 'pino';
import fs from 'fs/promises';
import { createStream } from 'rotating-file-stream';
import path from 'path';
import { CONFIG } from '../../config/index.js';

const { level, filePath, rotation } = CONFIG.Logger;

// Ensure logs folder exists
await fs.mkdir(path.dirname(filePath), { recursive: true });

// Setup rotating file stream
export const logDest = createStream(path.basename(filePath), {
  path: path.dirname(filePath),
  interval: rotation.interval,
  maxFiles: parseInt(rotation.maxFiles, 10),
});

// Logger options
const loggerOptions = process.env.NODE_ENV === 'test' ? { level: 'silent' } : { level };

// Multi-stream setup
const streams = [{ level: level as pino.Level, stream: logDest }];

if (process.env.NODE_ENV !== 'production') {
  // pretty console output in dev
  streams.push({
    level: level as pino.Level,

    stream: pino.transport({
      target: 'pino-pretty',

      options: {
        colorize: true,

        translateTime: 'SYS:standard',

        ignore: 'pid,hostname',
      },
    }),
  });
}

// Create logger with built-in multistream
const LOGGER = pino(loggerOptions, pino.multistream(streams));

export default LOGGER;
