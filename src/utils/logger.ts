import { pino } from 'pino';
import fs from 'fs';
import { createStream } from 'rotating-file-stream';
import path from 'path';
import yaml from 'js-yaml';
import { LoggerConfig, YamlConfig } from '../types/config.js';

// Load YAML config
let config: YamlConfig = {};
try {
  const file = fs.readFileSync('./config.yaml', 'utf8');
  config = yaml.load(file) as YamlConfig;
} catch (err) {
  console.error('❌ Failed to load config.yaml', err);
  process.exit(1);
}

const loggerConfig: LoggerConfig = {
  Logger: {
    level: config.Logger?.level || 'info',
    prettyPrint: config.Logger?.prettyPrint ?? true,
    filePath: config.Logger?.filePath || './logs/app.log',
    rotation: {
      interval: config.Logger?.rotation?.interval || '1d',
      maxFiles: config.Logger?.rotation?.maxFiles || '7',
    },
  },
};

const { level, filePath, rotation } = loggerConfig.Logger;

// Ensure logs folder exists
fs.mkdirSync(path.dirname(filePath), { recursive: true });

// Setup rotating file stream
const logDest = createStream(path.basename(filePath), {
  path: path.dirname(filePath),
  interval: rotation.interval,
  maxFiles: parseInt(rotation.maxFiles, 10),
});

// Logger options
const loggerOptions = process.env.NODE_ENV === 'test' ? { level: 'silent' } : { level };

// Multi-stream setup
const streams: { stream: any }[] = [
  { stream: logDest }, // always log to file
];

if (process.env.NODE_ENV !== 'production') {
  // pretty console output in dev
  streams.push({
    stream: pino.transport({
      target: 'pino-pretty',
      options: {
        colorize: true,
        translateTime: 'SYS:standard',
        ignore: 'pid,hostname',
      },
    }).stream,
  });
}

// Create logger with built-in multistream
const LOGGER = pino(loggerOptions, pino.multistream(streams));

LOGGER.info('🚀 Logger initialized with Pino built-in multistream');

export default LOGGER;
