import {pino} from "pino";
import fs from "fs";
import {CONFIG} from "../../config/index.js";
import { createStream } from "rotating-file-stream";
import path from "path";



const { level, prettyPrint, filePath, rotation } = CONFIG.Logger;

// Ensure logs folder exists
if (filePath) {
  fs.mkdirSync(path.dirname(filePath), { recursive: true });
}

// Setup rotating file stream
let logDest: any;
if (filePath) {
  logDest = createStream(path.basename(filePath), {
    path: path.dirname(filePath),
    interval: rotation?.interval || "1d",
    maxFiles: parseInt(rotation?.maxFiles,10) || 7,
  });
}

// Build logger options
const loggerOptions =
  process.env.NODE_ENV === "test"
    ? { level: "silent" }
    : {
        level,
        ...(prettyPrint
          ? {
              transport: {
                target: "pino-pretty",
                options: {
                  colorize: true,
                  translateTime: "SYS:standard",
                  ignore: "pid,hostname",
                },
              },
            }
          : {}),
      };

// Create logger
const LOGGER = logDest ? pino(loggerOptions, logDest) : pino(loggerOptions);

export default LOGGER;
