import { pino } from 'pino';
import fs from 'fs';
import { LoggerConfig } from '../types/config.js';
import { createStream } from 'rotating-file-stream';
import path from 'path';
import yaml from 'js-yaml';
import { YamlConfig } from '../types/config.js';

// Load YAML config
let config: YamlConfig = {};
try {
  const file = fs.readFileSync('./config.yaml', 'utf8');
  config = yaml.load(file) as YamlConfig;
} catch (err) {
  console.error('❌ Failed to load config.yaml', err);
  process.exit(1);
}

const loggercongfig: LoggerConfig = {
  Logger: {
    level: config.Logger?.level || 'info',
    prettyPrint: config.Logger?.prettyPrint || false,
    filePath: config.Logger?.filePath || '',
    rotation: {
      interval: config.Logger?.rotation?.interval || '1d',
      maxFiles: config.Logger?.rotation?.maxFiles || '7',
    },
  },
};

const { level, prettyPrint, filePath, rotation } = loggercongfig.Logger;

// Ensure logs folder exists
if (filePath) {
  fs.mkdirSync(path.dirname(filePath), { recursive: true });
}

// Setup rotating file stream
let logDest: any;
if (filePath) {
  logDest = createStream(path.basename(filePath), {
    path: path.dirname(filePath),
    interval: rotation?.interval || '1d',
    maxFiles: parseInt(rotation?.maxFiles, 10) || 7,
  });
}

// Build logger options
const loggerOptions =
  process.env.NODE_ENV === 'test'
    ? { level: 'silent' }
    : {
        level,
        ...(prettyPrint
          ? {
              transport: {
                target: 'pino-pretty',
                options: {
                  colorize: true,
                  translateTime: 'SYS:standard',
                  ignore: 'pid,hostname',
                },
              },
            }
          : {}),
      };

// Create logger
const LOGGER = logDest ? pino(loggerOptions, logDest) : pino(loggerOptions);

export default LOGGER;
