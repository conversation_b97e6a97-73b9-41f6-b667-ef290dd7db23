import { parsePhoneNumberFromString } from 'libphonenumber-js';

/**
 * Validates a phone number against a predefined set of allowed countries.
 *
 * @param phoneNumber The phone number string to validate.
 * @returns {boolean} True if the phone number is valid false otherwise.
 */
const isValidPhoneNumber = (phoneNumber: string): boolean => {
  const phone = parsePhoneNumberFromString(phoneNumber.trim());

  // Use a guard clause for early exit.
  if (!phone) {
    return false;
  }

  return phone.isValid();
};

export default isValidPhoneNumber;
