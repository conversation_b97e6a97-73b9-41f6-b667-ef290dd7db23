import { parsePhoneNumberFromString } from 'libphonenumber-js';

// Allowed countries' code
const ALLOWED_COUNTRIES = new Set(['IN', 'KE', 'AU']);

/**
 * Validates a phone number against a predefined set of allowed countries.
 *
 * @param phoneNumber The phone number string to validate.
 * @returns {boolean} True if the phone number is valid and from an allowed country, false otherwise.
 */
const isValidPhoneNumber = (phoneNumber: string): boolean => {
  const phone = parsePhoneNumberFromString(phoneNumber.trim());

  // Use a guard clause for early exit.
  if (!phone) {
    return false;
  }

  // Check if the parsed number is valid and if its country is in the allowed Set.
  const isAllowedCountry = ALLOWED_COUNTRIES.has(phone.country as string);

  return phone.isValid() && isAllowedCountry;
};

export default isValidPhoneNumber;
