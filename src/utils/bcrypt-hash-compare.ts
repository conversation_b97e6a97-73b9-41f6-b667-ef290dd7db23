import bcrypt from 'bcrypt';

/**
 * Hashes a given input string using bcrypt with a specified number of salt rounds.
 * The number of rounds is configurable via the BCRYPT_HASH_ROUNDS environment variable, defaulting to 10.
 *
 * @param {string} input - The string to be hashed.
 * @returns {Promise<string>} - A promise that resolves to the hashed string.
 */

export const hashString = async (input: string): Promise<string> => {
  const rounds = parseInt(process.env.BCRYPT_HASH_ROUNDS || '10', 10);
  const salt = await bcrypt.genSalt(rounds);
  return await bcrypt.hash(input, salt);
};

/**
 * Compares an input string with a previously hashed string to verify if they match.
 * Uses bcrypt's compare function for secure validation.
 *
 * @param {string} input - The plain text string to verify.
 * @param {string} hashedString - The hashed string to compare against.
 * @returns {Promise<boolean>} - A promise that resolves to true if the input matches the hash; false otherwise.
 */
export const compareHash = async (input: string, hashedString: string): Promise<boolean> => {
  const result = await bcrypt.compare(input, hashedString);
  return result;
};
