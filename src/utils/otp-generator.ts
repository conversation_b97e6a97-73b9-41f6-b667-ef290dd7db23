import crypto from 'crypto';

/**
 * Generates a secure 4-digit OTP using Node's CSPRNG.
 *
 * - Uses `crypto.randomInt(min, max)` which is cryptographically secure.
 * - The range (1000–9999) guarantees exactly 4 digits.
 * - This approach avoids the need for padding or string concatenation.
 *
 * @returns {string} The 4-digit OTP as a string.
 */
const generateOTP = (): string => {
  // Generate a random integer in the range [1000, 10000).
  const otp = crypto.randomInt(1000, 10000).toString();
  // Convert the number to a string for consistency in return type.
  return otp;
};

export default generateOTP;
