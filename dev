#!/bin/bash

# Rishi Core - Development Helper Script
# This is a convenience wrapper for the main development scripts

set -e

# Show usage if no arguments provided
if [ $# -eq 0 ]; then
    echo "🚀 Rishi Core Development Helper"
    echo ""
    echo "Usage: ./dev <command> [options]"
    echo ""
    echo "Commands:"
    echo "  setup           - Set up the development environment"
    echo "  node            - Switch to the correct Node.js version"
    echo "  run             - Lint, build and run the application"
    echo "  test            - Run, Unit , E2E and SAST tests in the application"
    echo "  run --skip-lint - Build and run without linting"
    echo "  run --lint-only - Only run linting check"
    echo "  run --lint-fix  - Fix linting and formatting issues"
    echo "  migration       - Run database migrations"
    echo ""
    echo "Examples:"
    echo "  ./dev setup"
    echo "  ./dev node"
    echo "  ./dev run"
    echo "  ./dev test"
    echo "  ./dev run --lint-fix"
    echo ""
    exit 0
fi

# Parse the command
COMMAND=$1
shift  # Remove the command from arguments

case $COMMAND in
    setup)
        echo "🔧 Running setup script..."
        script/setup.sh "$@"
        ;;
    node)
        echo "📦 Switching to correct Node.js version..."
        script/use-node.sh "$@"
        ;;
    run)
        echo "🚀 Running development script..."
        script/run.sh "$@"
        ;;
    test)
        echo "🚀 Running development script..."
        script/tests.sh "$@"
        ;;
     migration)
        echo "🚀 Running development script..."
        script/migration.sh "$@"
        ;; 
    db:seed)
        echo "🌱 Running database seeding script..."
        script/db_seed_phone_numbers.sh "$@"
        ;;   
    *)
        echo "❌ Unknown command: $COMMAND"
        echo "Run './dev' without arguments to see available commands."
        exit 1
        ;;
esac
