import { MigrationBuilder } from 'node-pg-migrate';
import dotenv from 'dotenv';

dotenv.config({ path: '.env' });

export async function up(pgm: MigrationBuilder): Promise<void> {
  pgm.createTable('otp', {
    id: {
      type: 'uuid',
      primaryKey: true,
      default: pgm.func('gen_random_uuid()'),
    },
    otp_data: {
      type: 'jsonb',
      notNull: true,
    },
  });

  // Create a GIN index on the otp_data JSONB column for efficient querying of keys.
  pgm.createIndex('otp', 'otp_data', { method: 'gin' });

  // Grant permissions to the application user
  pgm.sql(`GRANT SELECT, INSERT, UPDATE, DELETE ON "otp" TO ${process.env.DB_APP_USER}`);
}

export async function down(pgm: MigrationBuilder): Promise<void> {
  // Revoke permissions before dropping the table
  pgm.sql(`REVOKE SELECT, INSERT, UPDATE, DELETE ON "otp" FROM ${process.env.DB_APP_USER}`);

  // Drop index first, as it depends on the table.
  pgm.dropIndex('otp', 'otp_data');

  // Then, drop the table.
  pgm.dropTable('otp');
}
