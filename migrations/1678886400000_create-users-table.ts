import { MigrationBuilder } from 'node-pg-migrate';
import dotenv from 'dotenv';

dotenv.config({ path: '.env' });

export async function up(pgm: MigrationBuilder): Promise<void> {
  pgm.createTable('users', {
    id: {
      type: 'uuid',
      primaryKey: true,
      default: pgm.func('gen_random_uuid()'),
    },
    user_data: {
      type: 'jsonb',
      notNull: true,
    },
  });

  // Create a unique index on the 'phone_number' key within the user_data JSONB.
  // This enforces data integrity by preventing multiple users with the same phone number.
  pgm.createIndex('users', "((user_data->>'phone_number'))", { unique: true });

  // Grant permissions to the application user
  pgm.sql(`GRANT SELECT, INSERT, UPDATE, DELETE ON "users" TO ${process.env.DB_APP_USER}`);
}

export async function down(pgm: MigrationBuilder): Promise<void> {
  // Revoke permissions before dropping the table
  pgm.sql(`REVOKE SELECT, INSERT, UPDATE, DELETE ON "users" FROM ${process.env.DB_APP_USER}`);

  // Drop index first, as it depends on the table.
  pgm.dropIndex('users', "((user_data->>'phone_number'))");

  // Then, drop the table.
  pgm.dropTable('users');
}
