#!/bin/bash
set -euo pipefail

# Migration Script for Rishi Core
# For detailed documentation, see: docs/schema.md

show_help() {
    echo "🔧 Rishi Core Migration Script"
    echo ""
    echo "USAGE:"
    echo "  ./script/migration.sh [DIRECTION] [COUNT|FILE] [OPTIONS]"
    echo ""
    echo "PARAMETERS:"
    echo "  DIRECTION    Migration direction (default: up)"
    echo "               up   - Apply migrations"
    echo "               down - Rollback migrations"
    echo ""
    echo "  COUNT        Number of migrations to run (default: Infinity for all)"
    echo "               Can be a number (1, 2, 3...) or 'Infinity'"
    echo ""
    echo "  FILE         Specific migration file to run (partial name matching)"
    echo "               e.g., 'create-users', 'otp-table', '1678886400000'"
    echo ""
    echo "OPTIONS:"
    echo "  --help, -h   Show this help message"
    echo ""
    echo "EXAMPLES:"
    echo "  ./script/migration.sh                    # Run all pending migrations"
    echo "  ./script/migration.sh up 1               # Run 1 migration up"
    echo "  ./script/migration.sh down 2             # Rollback 2 migrations"
    echo "  ./script/migration.sh up create-users    # Run specific migration"
    echo "  ./script/migration.sh down otp-table     # Rollback specific migration"
    echo ""
    echo "DOCUMENTATION:"
    echo "  For detailed schema and migration documentation, see: docs/schema.md"
    echo ""
    echo "ENVIRONMENT:"
    echo "  Requires .env file with: DB_MIGRATION_USER, DB_MIGRATION_PASSWORD,"
    echo "  DB_HOST, DB_PORT, DB_NAME"
    echo ""
}

# Check for help flag
if [[ "${1:-}" == "--help" || "${1:-}" == "-h" ]]; then
    show_help
    exit 0
fi

# Ensure .env file exists
if [ ! -f .env ]; then
    echo "❌ Error: .env file not found. Please create it with DB_MIGRATION_USER, DB_MIGRATION_PASSWORD, DB_HOST, DB_PORT, DB_NAME."
    echo ""
    echo "Run './script/migration.sh --help' for more information."
    exit 1
fi

# Load environment variables from .env
set -a # automatically export all variables
source .env
set +a # stop automatically exporting

# Verify required variables are set
REQUIRED_VARS=(DB_MIGRATION_USER DB_MIGRATION_PASSWORD DB_HOST DB_PORT DB_NAME)

for VAR in "${REQUIRED_VARS[@]}"; do
    if [ -z "${!VAR:-}" ]; then
        echo "❌ Error: $VAR is not set in .env"
        exit 1
    fi
done

# Construct DATABASE_URL
export DATABASE_URL="postgres://${DB_MIGRATION_USER}:${DB_MIGRATION_PASSWORD}@${DB_HOST}:${DB_PORT}/${DB_NAME}"

# Parse parameters
DIRECTION=${1:-"up"}
PARAM2=${2:-"Infinity"}

# Check if second parameter is a file name (contains letters) or count (only numbers)
if [[ "$PARAM2" =~ ^[0-9]+$|^Infinity$ ]]; then
    # It's a count
    COUNT="$PARAM2"
    FILE=""
    echo "📦 Running $COUNT migration(s) $DIRECTION with user=$DB_MIGRATION_USER on db=$DB_NAME at $DB_HOST:$DB_PORT"
else
    # It's a file name - find matching migration file
    FILE=""
    COUNT="1"

    # Search for migration file that contains the provided string
    for migration_file in migrations/*"$PARAM2"*.ts; do
        if [[ -f "$migration_file" ]]; then
            # Extract just the filename without path and extension
            FILE=$(basename "$migration_file" .ts)
            echo "📦 Found migration file: $migration_file"
            break
        fi
    done

    if [[ -z "$FILE" ]]; then
        echo "❌ Error: No migration file found matching '$PARAM2'"
        echo "Available migration files:"
        ls -1 migrations/*.ts 2>/dev/null | sed 's/migrations\///g' | sed 's/\.ts$//g' | sed 's/^/  - /'
        exit 1
    fi

    echo "📦 Running specific migration '$FILE' $DIRECTION with user=$DB_MIGRATION_USER on db=$DB_NAME at $DB_HOST:$DB_PORT"
fi

# Build node-pg-migrate command
if [[ -n "$FILE" ]]; then
    # Run specific file - node-pg-migrate takes migration name as positional argument
    npx node-pg-migrate "$DIRECTION" "$FILE" -m migrations --migrations-table pgmigrations --verbose
else
    # Run with count
    npx node-pg-migrate "$DIRECTION" -c "$COUNT" -m migrations --migrations-table pgmigrations --verbose
fi
