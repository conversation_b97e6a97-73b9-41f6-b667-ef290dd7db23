#!/bin/bash

# Seed or delete phone numbers in the database for testing
# For detailed documentation, see: docs/SEEDING.md

set -euo pipefail

# Check if yq exists
if command -v yq &>/dev/null; then
    version=$(yq --version 2>&1 || true)

    # Check if it's the Go version (<PERSON>)
    if [[ "$version" =~ yq\ \(https://github.com/mikefarah/yq/\) ]]; then
        echo "✅ Found yq (Go version): $version"
        echo "🎉 yq is ready to use."
    else
        echo "⚠️ Found yq but not the Go version: $version"
        echo "🔄 Installing correct yq version..."

        if [[ "$OSTYPE" == "linux-gnu"* ]]; then
            sudo snap install yq
        elif [[ "$OSTYPE" == "darwin"* ]]; then
            brew install yq
        else
            echo "❌ Unsupported OS. Please install yq manually from https://github.com/mikefarah/yq"
            exit 1
        fi
    fi

else
    echo "⚠️ yq not found."
    echo "🔄 Installing yq (Go version)..."

    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        sudo snap install yq
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        brew install yq
    else
        echo "❌ Unsupported OS. Please install yq manually from https://github.com/mikefarah/yq"
        exit 1
    fi
fi

CONFIG_FILE="config.yaml"

# Check if config file exists
if [ ! -f "$CONFIG_FILE" ]; then
    echo "❌ Config file $CONFIG_FILE not found in root folder!"
    exit 1
fi

# Load environment variables from .env
if [ -f ".env" ]; then
    export $(grep -v '^#' .env | xargs)
else
    echo "❌ .env file not found!"
    exit 1
fi

# Ensure required env vars exist
: "${DB_HOST:?Missing DB_HOST in .env}"
: "${DB_PORT:?Missing DB_PORT in .env}"
: "${DB_APP_USER:?Missing DB_APP_USER in .env}"
: "${DB_APP_PASSWORD:?Missing DB_APP_PASSWORD in .env}"
: "${DB_NAME:?Missing DB_NAME in .env}"

# Parse arguments
DELETE_MODE=false
while getopts ":d" opt; do
    case $opt in
    d) DELETE_MODE=true ;;
    \?)
        echo "❌ Invalid option: -$OPTARG"
        exit 1
        ;;
    esac
done

# Export password so psql won’t prompt
export PGPASSWORD="$DB_APP_PASSWORD"

if [ "$DELETE_MODE" = true ]; then
    echo "🗑️  Preparing to delete phone numbers from database: $DB_NAME"

    DELETE_PHONE_NUMBERS=$(yq -r '.DeletePhoneNumbers[]' "$CONFIG_FILE")

    echo "⚠️  The following numbers are listed for deletion in $CONFIG_FILE:"
    for number in $DELETE_PHONE_NUMBERS; do
        echo "   - $number"
    done

    # Ask for confirmation
    read -p "❓ Are you sure you want to delete these numbers from the database? (y/N): " CONFIRM
    case "$CONFIRM" in
    [yY][eE][sS] | [yY])
        echo "✅ Proceeding with deletion..."
        ;;
    *)
        echo "❌ Deletion cancelled by user."
        exit 0
        ;;
    esac

    for number in $DELETE_PHONE_NUMBERS; do
        echo "🔍 Validating $number..."

        # Validate & format using libphonenumber-js
        FORMATTED=$(node -e "
      import { parsePhoneNumberFromString } from 'libphonenumber-js';
      const num = parsePhoneNumberFromString('$number');
      if (!num || !num.isValid()) process.exit(1);
      console.log(num.number);
    " || true)

        if [ -z "$FORMATTED" ]; then
            echo "⚠️ Skipping invalid number: $number"
            continue
        fi

        # Check if formatted number exists
        EXISTS=$(psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_APP_USER" -d "$DB_NAME" -t -A -c \
            "SELECT 1 FROM users WHERE user_data->>'phone_number' = '$FORMATTED' LIMIT 1;")

        if [ "$EXISTS" = "1" ]; then
            echo "✅ Deleting number: $FORMATTED"
            psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_APP_USER" -d "$DB_NAME" \
                -c "DELETE FROM users WHERE user_data->>'phone_number' = '$FORMATTED';"
        else
            echo "⚠️ Number not found in DB: $FORMATTED (skipping)"
        fi
    done

    echo "🎉 Done deleting phone numbers!"
else
    echo "📦 Seeding phone numbers into database: $DB_NAME"

    ADD_PHONE_NUMBERS=$(yq -r '.AddPhoneNumbers[]' "$CONFIG_FILE")

    for number in $ADD_PHONE_NUMBERS; do
        echo "🔍 Validating $number..."

        # Validate & format
        FORMATTED=$(node -e "
      import { parsePhoneNumberFromString } from 'libphonenumber-js';
      const num = parsePhoneNumberFromString('$number');
      if (!num || !num.isValid()) process.exit(1);
      console.log(num.number);
    " || true)

        if [ -z "$FORMATTED" ]; then
            echo "⚠️ Skipping invalid number: $number"
            continue
        fi

        # Check if number already exists in DB
        EXISTS=$(psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_APP_USER" -d "$DB_NAME" -t -A -c \
            "SELECT 1 FROM users WHERE user_data->>'phone_number' = '$FORMATTED' LIMIT 1;")

        if [ "$EXISTS" = "1" ]; then
            echo "ℹ️ Number already exists in DB: $FORMATTED"
            continue
        fi

        # Insert new number (wrap in JSONB)
        echo "✅ Inserting $FORMATTED into DB..."
        psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_APP_USER" -d "$DB_NAME" \
            -c "INSERT INTO users (user_data) VALUES ('{\"phone_number\": \"$FORMATTED\"}'::jsonb);"
    done

    echo "🎉 Done seeding phone numbers!"
fi
