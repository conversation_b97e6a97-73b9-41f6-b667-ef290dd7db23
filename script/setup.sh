#!/bin/bash

# Rishi Core - Setup Script
# This script sets up the development environment for the Rishi Core project

set -e # Exit on any error

# Database setup operation
echo "Database setup"
echo

CONTAINER_NAME="rishiflow-db"
HOST_DATA_DIR="$HOME/.rishiflow/db/postgresql"
HOST_PORT=5432
INIT_DB=false # default

if [ -x "$(command -v podman)" ]; then

    if podman container exists "$CONTAINER_NAME"; then

        # Check if container is running
        if podman container inspect -f '{{.State.Running}}' "$CONTAINER_NAME" | grep -q "true"; then
            echo "Container '$CONTAINER_NAME' is already running."
        else
            echo "Starting existing container '$CONTAINER_NAME'..."
            podman start "$CONTAINER_NAME"
        fi

        echo "✅ Using existing container (database setup will be skipped)"
    else
        # Prompt for password
        read -sp "Enter a password to be used for your PostgreSQL database: " password
        echo

        # Ensure the data directory exists
        if [ ! -d "$HOST_DATA_DIR" ]; then
            echo "Creating data directory at $HOST_DATA_DIR..."
            mkdir -p "$HOST_DATA_DIR"
        fi

        #Check if the port is free
        # Function to check if a port is free
        is_port_free() {
            # Check if a port is in use by a listening process
            if ss -ltn | awk '{print $4}' | grep -q ":$1$"; then
                return 1 # Port is NOT free
            else
                return 0 # Port IS free
            fi
        }

        # Loop until we find a free port
        while true; do
            echo "Checking if port $HOST_PORT is free..."
            if is_port_free "$HOST_PORT"; then
                echo "✅ Free port found: $HOST_PORT"
                break
            else
                echo "Port $HOST_PORT is in use. Checking next port..."
                HOST_PORT=$((HOST_PORT + 1))
            fi
        done
        # Run new container
        podman run -dt --name "$CONTAINER_NAME" \
            -e POSTGRES_PASSWORD="$password" \
            -v "$HOST_DATA_DIR:/var/lib/postgresql/data" \
            -p "$HOST_PORT:5432" \
            docker.io/library/postgres:17
        echo "Container '$CONTAINER_NAME' has been created and started."
        INIT_DB=true
    fi

else
    echo "Please ensure that you have installed podman in your system"
    echo "Please visit https://podman.io/docs/installation to check installation procedures for your system."
    exit 1
fi

# Database and User Setup
echo ""
echo "🗄️  Setting up database and users..."

# Wait for PostgreSQL to be ready
echo "⏳ Waiting for PostgreSQL to be ready..."
for i in {1..15}; do
    if podman exec "$CONTAINER_NAME" pg_isready -U postgres >/dev/null 2>&1; then
        echo "✅ PostgreSQL is ready!"
        break
    fi

    if [ $i -eq 15 ]; then
        echo "❌ PostgreSQL failed to start after 15 attempts"
        exit 1
    fi

    echo "   Attempt $i/15: PostgreSQL not ready yet, waiting 2 seconds..."
    sleep 2
done

if [ "$INIT_DB" = true ]; then
    # Database configuration
    DB_NAME="rishiflow"
    DB_OWNER="rishiflow"
    APP_USER="app_user"
    MIGRATION_USER="migration_user"

    # Generate random passwords for users
    DB_OWNER_PASSWORD=$(openssl rand -base64 32 | tr -d "=+/" | cut -c1-25)
    APP_USER_PASSWORD=$(openssl rand -base64 32 | tr -d "=+/" | cut -c1-25)
    MIGRATION_USER_PASSWORD=$(openssl rand -base64 32 | tr -d "=+/" | cut -c1-25)

    echo ""
    echo "📊 Creating database '$DB_NAME'..."

    # Create database with retry logic
    for i in {1..15}; do
        if podman exec "$CONTAINER_NAME" psql -U postgres -c "CREATE DATABASE $DB_NAME;" >/dev/null 2>&1; then
            echo "✅ Database '$DB_NAME' created successfully!"
            break
        fi

        # Check if database already exists
        if podman exec "$CONTAINER_NAME" psql -U postgres -lqt | cut -d \| -f 1 | grep -qw "$DB_NAME"; then
            echo "✅ Database '$DB_NAME' already exists!"
            break
        fi

        if [ $i -eq 15 ]; then
            echo "❌ Failed to create database after 15 attempts"
            exit 1
        fi

        echo "   Attempt $i/15: Database creation failed, retrying in 1 second..."
        sleep 1
    done

    echo ""
    echo "👤 Creating database users and setting up privileges..."

    # Create database owner
    echo "📝 Setting up database owner '$DB_OWNER'..."

    # Check if rishiflow user exists, create if not
    if podman exec "$CONTAINER_NAME" psql -U postgres -tAc "SELECT 1 FROM pg_roles WHERE rolname='$DB_OWNER'" | grep -q 1; then
        echo "   User '$DB_OWNER' already exists, updating password and privileges..."
        podman exec "$CONTAINER_NAME" psql -U postgres -c "ALTER USER $DB_OWNER WITH PASSWORD '$DB_OWNER_PASSWORD';"
    else
        echo "   Creating new database owner '$DB_OWNER'..."
        podman exec "$CONTAINER_NAME" psql -U postgres -c "CREATE USER $DB_OWNER WITH PASSWORD '$DB_OWNER_PASSWORD';"
    fi

    # Grant ownership of the database to rishiflow user
    podman exec "$CONTAINER_NAME" psql -U postgres -c "ALTER DATABASE $DB_NAME OWNER TO $DB_OWNER;"

    # Grant necessary privileges to the owner
    podman exec "$CONTAINER_NAME" psql -U postgres -c "
-- Grant CREATEDB privilege to allow creating additional databases if needed
ALTER USER $DB_OWNER CREATEDB;

-- Grant all privileges on the database
GRANT ALL PRIVILEGES ON DATABASE $DB_NAME TO $DB_OWNER;
"

    echo "✅ Database owner '$DB_OWNER' configured with full database ownership"

    # Create app_user with data manipulation privileges
    echo "📝 Setting up app_user with data manipulation privileges..."

    # Check if app_user exists, create if not
    if podman exec "$CONTAINER_NAME" psql -U postgres -d "$DB_NAME" -tAc "SELECT 1 FROM pg_roles WHERE rolname='$APP_USER'" | grep -q 1; then
        echo "   User '$APP_USER' already exists, updating password and privileges..."
        podman exec "$CONTAINER_NAME" psql -U postgres -d "$DB_NAME" -c "ALTER USER $APP_USER WITH PASSWORD '$APP_USER_PASSWORD';"
    else
        echo "   Creating new user '$APP_USER'..."
        podman exec "$CONTAINER_NAME" psql -U postgres -d "$DB_NAME" -c "CREATE USER $APP_USER WITH PASSWORD '$APP_USER_PASSWORD';"
    fi

    # Grant/update privileges for app_user
    podman exec "$CONTAINER_NAME" psql -U postgres -d "$DB_NAME" -c "
-- Grant connection to database
GRANT CONNECT ON DATABASE $DB_NAME TO $APP_USER;

-- Grant usage on schema
GRANT USAGE ON SCHEMA public TO $APP_USER;

-- Grant data manipulation privileges on all existing tables
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO $APP_USER;

-- Grant data manipulation privileges on all future tables
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT, INSERT, UPDATE, DELETE ON TABLES TO $APP_USER;

-- Grant usage on all sequences (for auto-increment columns)
GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO $APP_USER;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT USAGE ON SEQUENCES TO $APP_USER;
"

    echo "✅ app_user configured with data manipulation privileges"

    # Create migration_user with schema modification privileges
    echo "📝 Setting up migration_user with schema modification privileges..."

    # Check if migration_user exists, create if not
    if podman exec "$CONTAINER_NAME" psql -U postgres -d "$DB_NAME" -tAc "SELECT 1 FROM pg_roles WHERE rolname='$MIGRATION_USER'" | grep -q 1; then
        echo "   User '$MIGRATION_USER' already exists, updating password and privileges..."
        podman exec "$CONTAINER_NAME" psql -U postgres -d "$DB_NAME" -c "ALTER USER $MIGRATION_USER WITH PASSWORD '$MIGRATION_USER_PASSWORD';"
    else
        echo "   Creating new user '$MIGRATION_USER'..."
        podman exec "$CONTAINER_NAME" psql -U postgres -d "$DB_NAME" -c "CREATE USER $MIGRATION_USER WITH PASSWORD '$MIGRATION_USER_PASSWORD';"
    fi

    # Grant/update privileges for migration_user
    podman exec "$CONTAINER_NAME" psql -U postgres -d "$DB_NAME" -c "
-- Grant connection to database
GRANT CONNECT ON DATABASE $DB_NAME TO $MIGRATION_USER;

-- Grant usage and create on schema
GRANT USAGE, CREATE ON SCHEMA public TO $MIGRATION_USER;

-- Grant schema modification privileges on all existing tables
GRANT CREATE ON SCHEMA public TO $MIGRATION_USER;

-- Grant all privileges on all existing tables (needed for ALTER/DROP)
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO $MIGRATION_USER;

-- Grant all privileges on all future tables
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL PRIVILEGES ON TABLES TO $MIGRATION_USER;

-- Grant all privileges on sequences
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO $MIGRATION_USER;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL PRIVILEGES ON SEQUENCES TO $MIGRATION_USER;
"

    echo "✅ migration_user configured with schema modification privileges"

    # Create configuration files
    echo ""
    echo "💾 Creating configuration files..."

    # Create config.yaml (structure only, no sensitive data)
    cat >config.yaml <<EOF
# Database Connection Details and Server Configuration
# Generated by setup.sh on $(date)
# This file contains the structure - sensitive data is in .env

MainDatabase:
  host: localhost
  name: $DB_NAME

DatabaseOwner:
  user: $DB_OWNER

PostgresSuperuser:
  user: postgres

ApplicationUser:
  user: $APP_USER

MigrationUser:
  user: $MIGRATION_USER

# Server Configuration
Server:
host: 127.0.0.1

# Logger configuration
Logger:
  level: info
  prettyPrint: true
  filePath: ./logs/app.log
  rotation:
    interval: 1d
    maxFiles: 7



#Phone numbers to seed into db
AddPhoneNumbers:
   - +254712345678

#Phone numbers to Delete from the db
DeletePhoneNumbers:
   - +254712345678
EOF

    # Create .env file (sensitive data)
    cat >.env <<EOF
# Environment Configuration
# Generated by setup.sh on $(date)
# DO NOT COMMIT THIS FILE TO VERSION CONTROL

# Database Configuration
DB_HOST=localhost
DB_PORT=$HOST_PORT
DB_NAME=$DB_NAME

# Database Owner
DB_OWNER_USER=$DB_OWNER
DB_OWNER_PASSWORD=$DB_OWNER_PASSWORD

# PostgreSQL Superuser (for administration)
DB_POSTGRES_USER=postgres
DB_POSTGRES_PASSWORD=${password:-"CHANGE_ME_TO_YOUR_POSTGRES_PASSWORD"}

# Application User (Data Operations: SELECT, INSERT, UPDATE, DELETE)
DB_APP_USER=$APP_USER
DB_APP_PASSWORD=$APP_USER_PASSWORD

# Migration User (Schema Operations: CREATE, ALTER, DROP tables)
DB_MIGRATION_USER=$MIGRATION_USER
DB_MIGRATION_PASSWORD=$MIGRATION_USER_PASSWORD

# Server Configuration
SERVER_PORT=3000
SERVER_HOST=127.0.0.1

#Twilio Configuration
TWILIO_AUTH_SID=
TWILIO_AUTH_TOKEN=
TWILIO_SANDBOX_NUMBER=

#OTP life span in minutes
OTP_LIFE_MINS=5

EOF

    # Check if files were created successfully
    if [ -f "config.yaml" ] && [ -f ".env" ]; then
        echo "✅ Configuration files created successfully:"
        echo "  - config.yaml (structure and non-sensitive data)"
        echo "  - .env (passwords and sensitive configuration)"
    else
        echo "❌ Failed to create configuration files"
        exit 1
    fi
fi

# Change to project root directory (parent of script directory)
cd "$(dirname "$0")/.."

echo "🚀 Setting up Rishi Core development environment..."

# Node.js version to install
NODE_VERSION="22"
REQUIRED_VERSION="22.0.0"

# Check if nvm is installed
if ! command -v nvm &>/dev/null; then
    echo "📦 nvm not found. Installing nvm..."

    # Download and install nvm
    curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash

    # Source nvm script to make it available in current session
    export NVM_DIR="$HOME/.nvm"
    [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
    [ -s "$NVM_DIR/bash_completion" ] && \. "$NVM_DIR/bash_completion"

    if ! command -v nvm &>/dev/null; then
        echo "❌ Failed to install nvm. Please install it manually:"
        echo "   Visit: https://github.com/nvm-sh/nvm#installing-and-updating"
        exit 1
    fi

    echo "✅ nvm installed successfully"
else
    echo "✅ nvm is already installed"

    # Source nvm to ensure it's available
    export NVM_DIR="$HOME/.nvm"
    [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
    [ -s "$NVM_DIR/bash_completion" ] && \. "$NVM_DIR/bash_completion"
fi

# Install and use Node.js 22
# Check if Node.js 22 is already installed
echo "Checking for Node.js version $NODE_VERSION..."
if nvm ls | grep -q "v$NODE_VERSION"; then
    echo "✅ Node.js v$NODE_VERSION is already installed. Using it..."
    nvm use $NODE_VERSION
else
    # Install and use Node.js 22
    echo "📦 Node.js v$NODE_VERSION not found. Installing..."
    nvm install $NODE_VERSION
    nvm use $NODE_VERSION
fi

# Verify Node.js installation
if ! command -v node &>/dev/null; then
    echo "❌ Node.js installation failed"
    exit 1
fi

# Check Node.js version
CURRENT_NODE_VERSION=$(node --version | cut -d'v' -f2)
echo "✅ Node.js v$CURRENT_NODE_VERSION is now active"

# Verify we have the correct major version
MAJOR_VERSION=$(echo $CURRENT_NODE_VERSION | cut -d'.' -f1)
if [ "$MAJOR_VERSION" != "$NODE_VERSION" ]; then
    echo "⚠️  Expected Node.js $NODE_VERSION but got $MAJOR_VERSION"
    echo "   Attempting to switch to Node.js $NODE_VERSION..."
    nvm use $NODE_VERSION
fi

# Check if npm is available
if ! command -v npm &>/dev/null; then
    echo "❌ npm is not available. This should come with Node.js."
    exit 1
fi

echo "✅ Node.js $(node --version) and npm $(npm --version) are available"

# Create .nvmrc file to lock Node.js version for the project
echo "📝 Creating .nvmrc file to lock Node.js version..."
echo "$NODE_VERSION" >.nvmrc
echo "✅ Created .nvmrc with Node.js $NODE_VERSION"

# Install dependencies
echo "📦 Installing dependencies..."
npm install

# Verify TypeScript installation
if ! npx tsc --version &>/dev/null; then
    echo "❌ TypeScript installation failed"
    exit 1
fi

echo "✅ TypeScript $(npx tsc --version) is available"

# Create necessary directories
echo "📁 Creating necessary directories..."
mkdir -p dist
mkdir -p logs

# Build the project to verify everything works
echo ""
echo "🔨 Building project to verify setup..."
npm run build

# Check if build was successful
if [ $? -eq 0 ]; then
    echo "✅ Build successful!"
else
    echo "❌ Build failed. Please check the errors above."
    exit 1
fi
#Run Migrations
echo "running migrations"
script/migration.sh

if [ "$INIT_DB" = true ]; then
    echo ""
    echo "📋 Database Setup Summary:"
    echo "  Database: $DB_NAME"
    echo "  Host: localhost:$HOST_PORT"
    echo ""
    echo "👥 Database Users and Privileges:"
    echo ""
    echo "  👑 $DB_OWNER (Database Owner):"
    echo "    - Full ownership of the '$DB_NAME' database"
    echo "    - Can create/drop tables, indexes, and manage database structure"
    echo "    - Use for: Database ownership and high-level administration"
    echo ""
    echo "  🔑 postgres (PostgreSQL Superuser):"
    echo "    - Full administrative access to all databases"
    echo "    - Can create/drop databases and users"
    echo "    - Use for: System-level database administration tasks"
    echo ""
    echo "  👤 $APP_USER (Application User):"
    echo "    - Privileges: SELECT, INSERT, UPDATE, DELETE"
    echo "    - Scope: All tables in public schema (current and future)"
    echo "    - Use for: Application runtime database operations"
    echo ""
    echo "  🔧 $MIGRATION_USER (Migration User):"
    echo "    - Privileges: CREATE, ALTER, DROP tables"
    echo "    - Scope: Full schema modification rights in public schema"
    echo "    - Use for: Database migrations and schema changes"
    echo ""
    echo "⚠️  Security Notes:"
    echo "  - User passwords are randomly generated and saved in .env file"
    echo "  - Keep .env file secure and do not commit to version control"
    echo "  - The .env file is already included in .gitignore"
    echo "  - config.yaml contains structure only (no sensitive data)"
    echo ""
    echo "📁 Configuration Files:"
    echo "  config.yaml - Application structure and non-sensitive settings"
    echo "  .env        - Passwords and sensitive configuration (DO NOT COMMIT)"
    echo ""
else
    echo ""
    echo "📋 Database Setup:"
    echo "  Using existing database container '$CONTAINER_NAME'"
    echo "  Host: localhost:$HOST_PORT"
    echo "  Database initialization was skipped (container already configured)"
    echo ""
    echo "⚠️  Note for existing containers:"
    echo "  - .env file created with placeholder for postgres password"
    echo "  - Update DB_POSTGRES_PASSWORD in .env with your actual postgres password"
    echo "  - Other database users may not exist if container was created outside this script"
    echo ""
fi
echo "🎉 Setup completed successfully!"
echo ""
echo "📋 Node.js Environment:"
echo "  Node.js: $(node --version)"
echo "  npm: $(npm --version)"
echo "  nvm: $(nvm --version)"
echo ""
echo "💡 To use this Node.js version in new terminal sessions:"
echo "  nvm use 22"
echo "  # or simply: nvm use (reads from .nvmrc)"
echo ""
echo "Available commands:"
echo "  npm run dev     - Run in development mode with hot reload"
echo "  npm run build   - Build the project for production"
echo "  npm run start   - Run the built project"
echo "  npm run lint    - Check code quality with ESLint"
echo "  npm run fix     - Fix linting and formatting issues"
echo "  npm run format  - Format code with Prettier"
echo "  npm run test    - Run unit and end-to-end tests"
echo ""
echo "You can also use the convenience script: run ./dev to get the commands"
echo ""
echo "Happy coding! 🚀"
