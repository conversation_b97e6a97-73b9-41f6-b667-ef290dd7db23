# Logger

The logger is responsible for recording application events. It uses the [pino](https://getpino.io/) library for high-performance logging and `rotating-file-stream` for log rotation.

## Configuration

The logger is configured through the `config.yaml` file in the root directory. The following options are available under the `Logger` section:

| Option        | Description                                                                                              | Default |
|---------------|----------------------------------------------------------------------------------------------------------|---------|
| `level`       | The minimum log level to record.                                                                         | `info`  |
| `prettyPrint` | Whether to format the logs in a human-readable format.                                                   | `true` |
| `filePath`    | The path to the log file. If not provided, logs will be printed to the console.                          | `null`  |
| `rotation`    | Configuration for log rotation.                                                                          |         |
| `rotation.interval` | The interval for log rotation (e.g., `1d` for daily, `1h` for hourly).                               | `1d`    |
| `rotation.maxFiles` | The maximum number of log files to keep.                                                           | `7`     |

### Example Configuration

```yaml
Logger:
  level: 'debug'
  prettyPrint: true
  filePath: 'logs/app.log'
  rotation:
    interval: '1d'
    maxFiles: '14'
```

## Log Levels

The following log levels are available, in order of severity:

| Level   | Description                                                                                             |
|---------|---------------------------------------------------------------------------------------------------------|
| `fatal` | The application is about to abort.                                                                      |
| `error` | A critical error occurred.                                                                              |
| `warn`  | A warning that something might be wrong.                                                                |
| `info`  | An informational message.                                                                               |
| `debug` | A message for debugging purposes.                                                                       |
| `trace` | A very detailed message for debugging purposes.                                                         |
| `silent`| No logs will be recorded.                                                                               |

## Usage

To use the logger in your code, import the `LOGGER` instance from `src/utils/logger.ts`:

```typescript
import LOGGER from '../utils/logger';

LOGGER.info('This is an informational message.');
LOGGER.error('This is an error message.');
```
