# Logger

The logger is responsible for recording application events. It uses the [pino](https://getpino.io/) library for high-performance logging and `rotating-file-stream` for log rotation.

## Configuration

The logger is configured through the `config.yaml` file in the root directory. The configuration is **decoupled from NODE_ENV** and provides fine-grained control over logging behavior. The following options are available under the `Logger` section:

| Option        | Description                                                                                              | Default |
|---------------|----------------------------------------------------------------------------------------------------------|---------|
| `level`       | The minimum log level to record.                                                                         | `info`  |
| `prettyPrint` | Whether to format console logs in a human-readable format.                                               | `true`  |
| `filePath`    | The path to the log file.                                                                                | `./logs/app.log` |
| `enableConsole` | Whether to enable console logging.                                                                     | `true`  |
| `enableFile`  | Whether to enable file logging.                                                                          | `true`  |
| `rotation`    | Configuration for log rotation.                                                                          |         |
| `rotation.interval` | The interval for log rotation (e.g., `1d` for daily, `1h` for hourly).                               | `1d`    |
| `rotation.maxFiles` | The maximum number of log files to keep.                                                           | `7`     |
| `prettyOptions` | Configuration for pino-pretty formatting (when `prettyPrint` is enabled).                            |         |
| `prettyOptions.colorize` | Enable colored output in console logs.                                                        | `true`  |
| `prettyOptions.translateTime` | Time format for console logs.                                                               | `SYS:standard` |
| `prettyOptions.ignore` | Comma-separated list of keys to ignore in console output.                                        | `pid,hostname` |
| `prettyOptions.singleLine` | Display each log entry on a single line.                                                      | `false` |
| `prettyOptions.hideObject` | Hide object details in console output.                                                        | `false` |

### Example Configuration

```yaml
Logger:
  level: debug
  prettyPrint: true
  filePath: ./logs/app.log
  enableConsole: true
  enableFile: true
  rotation:
    interval: 1d
    maxFiles: 14
  prettyOptions:
    colorize: true
    translateTime: SYS:standard
    ignore: pid,hostname
    singleLine: false
    hideObject: false
```

### Configuration Scenarios

#### Development Environment
```yaml
Logger:
  level: debug
  prettyPrint: true
  enableConsole: true
  enableFile: true
  prettyOptions:
    colorize: true
    singleLine: false
```

#### Production Environment
```yaml
Logger:
  level: info
  prettyPrint: false
  enableConsole: false
  enableFile: true
  rotation:
    interval: 1d
    maxFiles: 30
```

#### Console Only (No File Logging)
```yaml
Logger:
  level: info
  prettyPrint: true
  enableConsole: true
  enableFile: false
```

#### File Only (No Console Output)
```yaml
Logger:
  level: warn
  prettyPrint: false
  enableConsole: false
  enableFile: true
```

## Log Levels

The following log levels are available, in order of severity:

| Level   | Description                                                                                             |
|---------|---------------------------------------------------------------------------------------------------------|
| `fatal` | The application is about to abort.                                                                      |
| `error` | A critical error occurred.                                                                              |
| `warn`  | A warning that something might be wrong.                                                                |
| `info`  | An informational message.                                                                               |
| `debug` | A message for debugging purposes.                                                                       |
| `trace` | A very detailed message for debugging purposes.                                                         |
| `silent`| No logs will be recorded.                                                                               |

## Features

### Environment Independence
The logger configuration is **completely decoupled from NODE_ENV**. You have full control over logging behavior through the configuration file, regardless of the environment.

### Multistream Support
The logger supports multiple output streams simultaneously:
- **Console output**: Pretty-formatted logs for development
- **File output**: JSON-formatted logs for production analysis
- **Flexible configuration**: Enable/disable streams independently

### Pino-Pretty Options
When `prettyPrint` is enabled, you can customize the console output format:
- **colorize**: Enable/disable colored output
- **translateTime**: Customize timestamp format
- **ignore**: Hide specific fields from console output
- **singleLine**: Compact single-line format
- **hideObject**: Hide object details for cleaner output

### Log Rotation
Automatic log file rotation with configurable:
- **Rotation interval**: Daily, weekly, monthly, or custom intervals
- **File retention**: Maximum number of rotated files to keep
- **Automatic cleanup**: Old files are automatically removed

## Usage

To use the logger in your code, import the `LOGGER` instance from `src/utils/logger.ts`:

```typescript
import LOGGER from '../utils/logger.js';

// Basic logging
LOGGER.info('This is an informational message.');
LOGGER.error('This is an error message.');

// Structured logging
LOGGER.info({ userId: 123, action: 'login' }, 'User logged in');
LOGGER.error({ error: 'Database connection failed', code: 500 }, 'Database error');
```

## Migration from NODE_ENV-based Configuration

If you were previously relying on NODE_ENV for logger behavior, update your configuration:

### Before (NODE_ENV-dependent)
```javascript
// Logger automatically determined behavior based on NODE_ENV
// - development: console + file with pretty printing
// - production: file only with JSON format
// - test: silent
```

### After (Configuration-driven)
```yaml
# Explicitly configure desired behavior
Logger:
  level: info
  enableConsole: true    # Control console output
  enableFile: true       # Control file output
  prettyPrint: true      # Control formatting
```

This provides better control and predictability across different deployment scenarios.
