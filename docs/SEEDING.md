# Database Seeding Guide

This document explains how to use the `db_seed_phone_numbers.sh` script to add or remove test phone numbers from the database.

## Purpose

The seeding script is a development utility designed to populate the `users` table with test phone numbers. It can also be used to clean up these numbers. This is useful for setting up a consistent state for manual testing or demonstrations.

## Requirements

Before running the script, ensure you have the following dependency installed:

### `yq`

The script uses `yq` to parse the `config.yaml` file. `yq` is a lightweight and portable command-line YAML processor.

**Installation:**

- **macOS (using Homebrew):**
  ```bash
  brew install yq
  ```

- **Linux (using `snap`):**
  ```bash
  sudo snap install yq
  ```

- **Linux (binary download):**
  ```bash
  sudo wget https://github.com/mikefarah/yq/releases/latest/download/yq_linux_amd64 -O /usr/bin/yq && sudo chmod +x /usr/bin/yq
  ```

For other installation methods, please refer to the official yq installation guide.

## How to Run the Script

You can run the script directly or use the `dev` convenience wrapper.

### Add Phone Numbers

This is the default mode. It reads the `AddPhoneNumbers` list from `config.yaml` and inserts them into the database.

```bash
# Using the dev helper (recommended)
./dev db:seed

# Or running the script directly
./script/db_seed_phone_numbers.sh
```

The script will validate each number, check if it already exists, and insert it if it's new.

### Delete Phone Numbers

To delete numbers, use the `-d` flag. This reads the `DeletePhoneNumbers` list from `config.yaml` and removes them from the database.

**You will be asked for confirmation before any data is deleted.**

```bash
# Using the dev helper (recommended)
./dev db:seed -d

# Or running the script directly
./script/db_seed_phone_numbers.sh -d
```

The script will validate each number and remove it if it exists in the database.

## Configuring Phone Numbers

All phone numbers are managed in the `config.yaml` file, which is created in the project root after running `./dev setup`.

### Adding a Number for Seeding


All phone numbers are managed in the `config.yaml` file, which is created in the project root after running `./dev setup`.  
Numbers must be provided in **YAML list format** and in **[E.164 international format](https://en.wikipedia.org/wiki/E.164)** (e.g., `+1234567890`).

```yaml
# config.yaml
AddPhoneNumbers:
   - +254712345678 
   - +14155552672
    # Add your new number here
```

### Adding a Number for Deletion

To remove a phone number from the database, add it to the DeletePhoneNumbers list in config.yaml using YAML list format.

```yaml
# config.yaml
DeletePhoneNumbers:
   - +14155552671 
   - +14155552672
   - +254712345678
   # Add the number you want to delete here
```